import { <PERSON>, Sun } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useTheme } from "next-themes";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";
import { logEvent } from "@/lib/analytics/events-client";

export const ThemeToggle = () => {
  const { setTheme, theme } = useTheme();
  
  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
    logEvent(SiteBehaviorEvent.TOGGLE_THEME, { theme: newTheme });
  };

  return (
    <div className="flex items-center gap-2 border-x border-border/30 lg:px-4 px-0 !sm:ml-4 !ml-0">
      <Sun 
        className={`md:h-6 md:w-6 h-[18px] w-[18px] md:block ${
          theme === "light" ? "block" : "hidden"
        }`} 
        color="#f59e0b" 
      />
      <Switch
        checked={theme === "dark"}
        onCheckedChange={toggleTheme}
        aria-label="Toggle dark mode"
        className="data-[state=checked]:bg-iqidis-vividPurple"
      />
      <Moon 
        className={`md:h-6 md:w-6 h-[18px] w-[18px] md:block ${
          theme === "dark" ? "block" : "hidden"
        }`} 
        color="#8b5cf6" 
      />
    </div>
  );
};
