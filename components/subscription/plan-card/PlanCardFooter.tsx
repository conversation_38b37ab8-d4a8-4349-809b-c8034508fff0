import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CardFooter } from "@/components/ui/card";
import { CreditCard, Pencil, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Subscription } from "@/lib/db/schema";
import { Separator } from "@radix-ui/react-separator";

interface PlanCardFooterProps {
  id: "free" | "premium" | "premium-yearly" | "enterprise";
  isProcessing: boolean;
  isUserPlan: boolean;
  onSubscribe: (
    tier: "free" | "premium" | "premium-yearly" | "enterprise"
  ) => void;
  activeSubscription?: Subscription;
  hideButtons?: boolean;
  billingCycle: "monthly" | "annual";
}

const PlanCardFooter = ({
  id,
  isProcessing,
  isUserPlan,
  onSubscribe,
  activeSubscription,
  hideButtons,
  billingCycle,
}: PlanCardFooterProps) => {
  const router = useRouter();
  const { data: session } = useSession();
  const isEnterprise = id === "enterprise";
  const isFreemium = id === "free";

  const isActivePremiumTier =
    !isFreemium && activeSubscription?.status === "active";

  // Determine if this is the user's current plan based on session data
  const isCurrentPlan = React.useMemo(() => {
    if (!session?.user) return false;

    // Direct match
    if (session.user.subscriptionTier === id) return true;

    // Consider premium and premium-yearly as the same plan for button disabling
    if (
      (session.user.subscriptionTier === "premium" ||
        session.user.subscriptionTier === "premium-yearly") &&
      (id === "premium" || id === "premium-yearly")
    ) {
      return true;
    }

    return false;
  }, [session, id]);

  // Button text for freemium plan when it's the user's current plan
  const getButtonText = () => {
    if (isProcessing) return "Processing...";
    if (isCurrentPlan && isFreemium) return "Continue with preview";
    // if (isCurrentPlan) return "Current Plan";
    if (isFreemium) return "Try Preview";
    if (isEnterprise) return "Contact Sales";
    if (isActivePremiumTier) return "Manage Subscription";
    return "Subscribe Now";
  };

  const handleClick = () => {
    const buttonText = getButtonText();
    if (buttonText === "Continue with preview") {
      router.push("/");
    } else {
      onSubscribe(id);
    }
  };

  return (
    <CardFooter>
      {!isEnterprise ? (
        <>
          {hideButtons ? (
            <div className="flex flex-grow items-center justify-center my-2">
              <Separator className="my-4" />
              <p className="text-sm text-gray-500 mb-2 text-center">
                Your account is managed by the administrator.
              </p>
            </div>
          ) : !isFreemium &&
            activeSubscription &&
            !activeSubscription.isTrialUsed ? (
            //  &&
            // billingCycle === "monthly"
            <>
              <Button
                className={`w-full h-12 gap-2`}
                variant="default"
                size={
                  id === "premium" || id === "premium-yearly" ? "xl" : "default"
                }
                onClick={handleClick}
                disabled={isProcessing || (isCurrentPlan && !isFreemium)}
              >
                {isProcessing ? (
                  <>
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    <span className="text-base">Processing...</span>
                  </>
                ) : (
                  <>
                    {id === "premium" || id === "premium-yearly" ? (
                      <CreditCard className="h-4 w-4" />
                    ) : null}
                   <span className="text-base md:block hidden">Start your 14-day free trial</span>
                    <span className="text-base md:hidden block">14-day free trial</span>
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button
              className={`w-full h-12 gap-2 !p-0`}
              variant={
                id === "premium" || id === "premium-yearly" || isFreemium
                  ? "default"
                  : "outline"
              }
              size={id === "premium" || isFreemium ? "xl" : "default"}
              onClick={handleClick}
              disabled={
                isProcessing
                // || (isCurrentPlan && !isFreemium)
              }
            >
              {isProcessing ? (
                <>
                  <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  <span className="text-base">Processing...</span>
                </>
              ) : (
                <>
                  {id === "premium" ? <CreditCard className="h-4 w-4" /> : null}
                  {isCurrentPlan && isFreemium ? (
                    <ArrowRight className="h-4 w-4" />
                  ) : null}
                  <span className="text-base">{getButtonText()}</span>
                </>
              )}
            </Button>
          )}
        </>
      ) : (
        <Button
          className="w-full h-12 gap-2"
          variant="outline"
          onClick={() => onSubscribe("enterprise")}
          disabled={isProcessing || isCurrentPlan}
        >
          {isProcessing ? (
            <>
              <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              <span>Processing...</span>
            </>
          ) : isCurrentPlan ? (
            <span>Current Plan</span>
          ) : (
            <>
              <Pencil className="h-4 w-4" />
              <span>Contact Sales</span>
            </>
          )}
        </Button>
      )}
    </CardFooter>
  );
};

export default PlanCardFooter;
