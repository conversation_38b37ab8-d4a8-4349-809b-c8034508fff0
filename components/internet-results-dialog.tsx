import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Markdown } from "./markdown";
import { But<PERSON> } from "./ui/button";
import { CrossIcon } from "./icons";
import { useState } from "react";
import { ChevronRight, ChevronLeft } from "lucide-react";

interface InternetResultsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  results?: {
    mainContent: string;
    citations: string[];
    formattedContent: string;
  };
}

export function InternetResultsDialog({
  isOpen,
  onOpenChange,
  results,
}: InternetResultsDialogProps) {
  const [showCitations, setShowCitations] = useState(true);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] p-0 overflow-hidden">
        <DialogHeader className="p-4 border-b">
          <div className="flex flex-row items-center justify-between">
            <DialogTitle>
              Summary of Online & Iqidis Database Authorities
            </DialogTitle>
            <DialogClose asChild>
              <Button variant="ghost" className="h-fit p-2">
                <CrossIcon size={18} />
              </Button>
            </DialogClose>
          </div>
        </DialogHeader>

        {results ? (
          <div className="flex h-[calc(90vh-80px)] w-full md:flex-row flex-col">
            {/* Main content panel - left page of the book */}
            <div
              className={`overflow-y-auto p-6 w-full md:h-full h-full ${
                showCitations ? "w-[65%]" : "w-full"
              }`}
            >
              {/* <h2 className="text-2xl font-semibold mb-4 text-primary">Summary of Online & Iqidis Database Authorities</h2> */}
              <div className="prose dark:prose-invert max-w-none">
                <Markdown>{results.mainContent}</Markdown>
              </div>
            </div>

            {/* Toggle button for citations */}
            <div className="flex items-center p-2 md:pb-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCitations(!showCitations)}
                className="h-8 w-8 p-0 rounded-full bg-gray-100 hover:bg-gray-200 border border-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700 dark:border-gray-600"
              >
                {showCitations ? (
                  <ChevronRight className="md:rotate-0 rotate-90" size={16} />
                ) : (
                  <ChevronLeft className="md:rotate-0 rotate-90"  size={16} />
                )}
              </Button>
            </div>

            {/* Citations panel - right page of the book */}
            {showCitations && (
              <div className="md:w-[35%] w-full overflow-y-auto p-4 pr-6 mr-2 bg-muted/30 shadow-inner">
                <h3 className="text-xl font-medium mb-6 border-b pb-2">
                  Authorities Used
                </h3>
                {results.citations && results.citations.length > 0 ? (
                  <div className="space-y-4">
                    {results.citations.map((citation, index) => (
                      <div
                        key={index}
                        className="p-4 rounded-md bg-card border text-sm hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-start gap-3">
                          <span className="font-bold text-blue-600 dark:text-blue-400 min-w-[24px] flex-shrink-0">
                            [{index + 1}]
                          </span>
                          <a
                            href={citation}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline dark:text-blue-400 break-all overflow-hidden"
                          >
                            {citation}
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No References Available
                  </p>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="p-6">
            <p>No results available</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
