'use client'

import { Share2, Download, Trash, Search, RefreshCw, FolderTree } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Button, Input, InputRef, Tooltip } from "antd";
import { useInterval, useRequest } from "ahooks";
import { UploadButton } from "./Upload/UploadButton";
import { UploadModal } from "./Upload/UploadModal";
import { Navbar } from "./Navbar";
import { DocumentTable } from "./DocumentTable";
import { useRefresh, useTableScrollY } from "./DocumentTable/hooks";
import { useDownloadDocuments } from "./DocumentTable/hooks";
import { DocumentTypeFilter } from "./DocumentTable/documentTypeFilter";
import { toast } from "sonner";
import { DeleteDocumentButton, DeleteDocumentModal } from "./DocumentTable/DeleteDocuments";
import { MoveDocumentButton, MoveDocumentModal } from "./DocumentTable/MoveDocuments";
import { getFolders } from "./request/folders";
import { PageLoader } from "../ui/page-loader";
import { usePathname, useRouter } from "next/navigation";

export const DocumentList = () => {
  const [typeFilters, setTypeFilters] = useState<string[]>([])
  const [fileName, setFileName] = useState<string>('')
  const fileNameRef = useRef<InputRef>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [dataSource, setDataSource] = useState<any[]>([])

  const [sort, setSort] = useState<[string, 'descend' | 'ascend'] | []>([])
  const [pagination, setPagination] = useState<{
    current?: number
    pageSize?: number
    total?: number
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const { refreshFlag, refresh: refreshDocuments } = useRefresh()

  const { data: { folders } = {} } = useRequest(() => {
    return getFolders()
  }, {
    refreshDeps: [refreshFlag]
  })

  const loadNeedLoading = useRef(true)
  const { loading: getDocumentsLoading } = useRequest(async () => {

    const queryString = new URLSearchParams({
      current: pagination.current?.toString() ?? '1',
      pageSize: pagination.pageSize?.toString() ?? '10',
    })
    if (fileNameRef.current?.input?.value) {
      queryString.append('filename', fileNameRef.current?.input?.value)
    }

    if (sort.length === 2) {
      queryString.append('order', `${sort[0]}_${sort[1]}`)
    }
    if (typeFilters.length > 0) {
      typeFilters.forEach(filter => {
        if (filter === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          queryString.append('fileType', 'application/msword')
        }
        queryString.append('fileType', filter)
      })
    }

    const response = await fetch(`/api/documents?${queryString.toString()}`, {
      method: 'GET',
    })

    const data = await response.json()

    setDataSource(data.documents)
    setPagination({
      current: data.pagination.current,
      pageSize: data.pagination.pageSize,
      total: data.pagination.total,
    })
    loadNeedLoading.current = false
  }, {
    debounceWait: 200,
    refreshDeps: [sort[0], sort[1], typeFilters, pagination.current, pagination.pageSize, fileName, refreshFlag],
    // manual: true,
  })

  useEffect(() => {
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    })
  }, [fileName, typeFilters])

  useEffect(() => {
    loadNeedLoading.current = true
  }, [sort[0], sort[1], typeFilters, pagination.current, pagination.pageSize, fileName])

  const { downloadDocumentsLoading, downloadDocumentsRequest } = useDownloadDocuments()

  useEffect(() => {
    if (fileNameRef.current?.input?.value) {
      setFileName(fileNameRef.current?.input?.value)
    }
  }, [fileNameRef.current?.input?.value])

  const [interval, setInterval] = useState<number | undefined>(undefined)

  const clear = useInterval(() => {
    console.log('interval', dataSource)
    if (dataSource.every(it => it.status === 'AVAILABLE')) {
      setInterval(undefined)
      clear()
    } else {
      refreshDocuments()
    }
  }, interval)

  const containerRef = useRef<HTMLDivElement>(null)
  const { y } = useTableScrollY({
    container: containerRef.current,
    offset: 234,
  })

  const checkRowsCanDownload = (keys: string[], datas: any[]) => {
    return keys.every(id => {
      const document = datas.find(doc => doc.id === id)
      return document?.status === 'AVAILABLE'
    })
  }

  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  // Intercept navigation to show loader
  const handleRoute = (path: string) => {
    // Set global loading state
    if (pathname !== path) {
      setIsLoading(true);
      router.push(path);
    }
  };

  return (
    <div className="oveflow-hidden h-full flex flex-col px-[30px] pb-8 pt-4 bg-[#F3F3F6]">  
      <div className="flex-1 overflow-hidden flex-col flex h-full" ref={containerRef}>
        <div className="flex lg:items-center justify-between gap-y-2 mb-3 md:flex-row flex-col">
          <div className="bg-white p-1 flex rounded-md w-fit libraryTabsDFS overflow-auto">
            <Navbar handleRoute={handleRoute}/>
          </div>
          <div className="flex gap-x-2 w-[460px] items-center">
            <Input
              placeholder="Search documents..."
              ref={fileNameRef}
              className="searchBarLibrary h-[32px] !bg-white"
              variant="borderless"
              prefix={<Search className="size-4 text-gray-medium" />}
              allowClear
              onPressEnter={() => {
                setFileName(fileNameRef.current?.input?.value ?? '')
              }}
              onBlur={() => {
                setFileName(fileNameRef.current?.input?.value ?? '')
              }}
              onClear={() => {
                setFileName('')
              }}
            />
            <DocumentTypeFilter typeFilters={typeFilters} setTypeFilters={setTypeFilters} />
            <div className="h-6 mx-4 w-[1px] bg-gray-light" />
            <UploadButton />
          </div>
          
        </div>
        <div className="flex-1 bg-white rounded-md ms:px-5 px-3 pt-5 overflow-hidden flex flex-col">
          <div className="flex sm:items-center justify-between flex-col sm:flex-row gap-y-2 sm:gap-y-0 mb-5 docHeadingBtn">
            <div className="flex items-center">
              <h2 className="text-2xl font-bold">Your Documents</h2>
              <RefreshCw className="ml-4 text-gray-medium size-4 cursor-pointer" onClick={() => refreshDocuments()} />
            </div>
            <div className="flex gap-x-3 items-center flex-1 justify-end">
              {selectedRowKeys.length > 0 && <>
                <span>{selectedRowKeys.length} selected</span>
                <div className="h-4 w-[1px] bg-gray-light" />
              </>}
              <Button
                color="default"
                variant="filled"
                disabled={selectedRowKeys.length === 0}
                onClick={() => {
                  if (!checkRowsCanDownload(selectedRowKeys, dataSource)) {
                    toast.error('Some documents are not available to download')
                    return
                  }
                  downloadDocumentsRequest(selectedRowKeys)
                }}
                loading={downloadDocumentsLoading}
              >
                <Download className="size-4" />
                <span className="md:block hidden">Download</span>
              </Button>

              <Tooltip title="Coming soon">
                <Button color="default" variant="filled" disabled>
                  <Share2 className="size-4" />
                 <span className="md:block hidden">Share</span>
                </Button>
              </Tooltip>

              <div className="h-4 w-[1px] bg-gray-light" />
              <MoveDocumentButton
                documents={dataSource.filter(it => selectedRowKeys.includes(it.id)).map(it => ({
                  documentId: it.id,
                  documentName: it.originalName,
                  folderId: it.folderId,
                  folderName: it.folderName,
                }))}
              >
                <Button
                  color="default"
                  variant="outlined"
                  disabled={selectedRowKeys.length === 0}
                >
                  <FolderTree className="size-4" />
                  Move Files
                </Button>
              </MoveDocumentButton>
              <DeleteDocumentButton
                documents={dataSource.filter(it => selectedRowKeys.includes(it.id)).map(it => ({
                  documentId: it.id,
                  documentName: it.originalName
                }))}
              >
                <Button
                  color="danger"
                  variant="outlined"
                  disabled={selectedRowKeys.length === 0}
                >
                  <Trash className="size-4" />
                  <span className="md:block hidden">Delete</span>
                </Button>
              </DeleteDocumentButton>
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            <DocumentTable
              sort={sort}
              setSort={setSort}
              loading={loadNeedLoading.current && getDocumentsLoading}
              dataSource={dataSource}
              setDataSource={setDataSource}
              selectedRowKeys={selectedRowKeys}
              setSelectedRowKeys={setSelectedRowKeys}
              pagination={pagination}
              setPagination={setPagination}
              tableProps={{
                scroll: {
                  y,
                }
              }}
            />
          </div>
        </div>
      </div>
      <UploadModal
        folderList={folders}
        onClose={(dirty) => {
          if (dirty) {
            setInterval(1000 * 5)
            refreshDocuments()
          }
        }}
      />
      <DeleteDocumentModal
        onConfirm={() => {
          refreshDocuments()
          setSelectedRowKeys([])
        }}
      />
      <MoveDocumentModal
        onConfirm={() => {
          refreshDocuments()
          setSelectedRowKeys([])
        }}
      />
      {isLoading && <PageLoader message="Loading..." />}
    </div>
  );
};