import { Upload, UploadFile, Button } from "antd"
import { toast } from "sonner"
import { UploadDocumentIcon } from "@/components/icons"
import { Plus, RotateCw, Trash } from 'lucide-react'
import { Dispatch, SetStateAction } from "react"

const Dragger = Upload.<PERSON>agger

export const UploadWhiteList = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']

export const UploadDragger = (props: {
  fileList: UploadFile[]
  setFileList: Dispatch<SetStateAction<UploadFile[]>>
  uploadLoading: boolean
  retryUploadLoading: boolean
  uploadStatus: 'done' | 'part-error' | 'idle' | 'uploading'
  retryUpload: (file: UploadFile) => Promise<void>
  themeMode?: string
}) => {
  const { fileList, setFileList, uploadLoading, retryUploadLoading, uploadStatus, retryUpload, themeMode } = props

  return (
    <Dragger className={`[&>.ant-upload-drag]:!border-2 [&_.ant-upload-btn]:!p-0
      ${fileList.length > 0 ? '[&>.ant-upload-drag]:!border-b-0 [&>.ant-upload-drag]:!rounded-b-none [&>.ant-upload-list]:border-2 [&>.ant-upload-list]:pb-2 [&>.ant-upload-list]:border-t-0 [&>.ant-upload-list]:border-dashed [&>.ant-upload-list]:border-gray-300 [&>.ant-upload-list]:rounded-b-lg [&>.ant-upload-list]:bg-gray-50 [&>.ant-upload-list]:px-4 [&>.ant-upload-drag:hover]:!border-gray-300' : ''}
      ${themeMode === 'dark' ? '[&>.ant-upload-drag]:!border-gray-700 [&>.ant-upload-drag]:!bg-gray-900 [&>.ant-upload-list]:!border-gray-700 [&>.ant-upload-list]:!bg-gray-900 [&_.ant-upload-btn]:!bg-gray-900 [&_.ant-upload-btn]:!text-white [&_.ant-upload-list-item]:!bg-gray-900 [&_.ant-upload-list-item]:!text-white [&>.ant-upload-drag:hover]:!border-gray-700' : ''}`}
        multiple
        maxCount={10}
        accept={['.pdf', '.doc', '.docx', '.txt'].join(',')}
        onChange={(info) => {
          setFileList(info.fileList)
        }}
        fileList={fileList}
        beforeUpload={(file, fileList) => {
          if (fileList.length > 10) {
            toast.error('You can only upload 10 files at one time')
            return Upload.LIST_IGNORE
          }

          if (file.size > 1024 * 1024 * 20) {
            toast.error(`File size exceeds 20MB: ${file.name}`)
            return Upload.LIST_IGNORE
          }

          if (!UploadWhiteList.includes(file.type)) {
            toast.error(`File type not supported: ${file.name}`)
            return Upload.LIST_IGNORE
          }
          return false
        }}
        showUploadList={{
          removeIcon: <Trash className="size-4 !text-function-error" />,
          showPreviewIcon: false,
          showRemoveIcon: !uploadLoading,
          showDownloadIcon: false,
        }}
        itemRender={(originNode, file) => {
          return <div className="pb-1 border-b border-gray-light flex items-center hover:bg-[rgba(0,0,0,0.04)] [&_.ant-upload-list-item]:hover:bg-none" key={file.uid}>
            <span className='flex-1'>{file.status === 'error' ? originNode.props.children : originNode}</span>
            {file.status === 'error' && !uploadLoading && !retryUploadLoading && <RotateCw className="cursor-pointer hover:bg-gray-light size-3.5 text-function-error mt-2" onClick={async () => {
              setFileList(prev => {
                const index = prev.findIndex(it => it.uid === file.uid)
                if (index !== -1) {
                  file.status = 'uploading'
                  prev[index] = file
                }
                return [...prev]
              })
              await retryUpload(file)
            }}/>}
          </div>
        }}
      >
        {fileList.length === 0 ? <div
          className={`w-full flex flex-col items-center justify-center py-10 gap-y-5 p-4 ${themeMode === 'dark' ? 'bg-gray-900 text-white' : ''}`}
          style={{ minHeight: 260 }}
        >
          <div className="flex flex-col items-center justify-center">
            <UploadDocumentIcon />
            <p className={`mt-6 text-base font-medium text-center mb-5 ${themeMode === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Choose “Select files” or drag files into this space.
            </p>
            <Button color="primary" variant="solid" className="w-[140px]" shape="round">
              Select files
            </Button>
          </div>
          <p className={`text-xs w-[510px] ${themeMode === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}>
            You may only upload 10 files at one time.<br />
            Supported file types: <span className="font-medium">PDF, DOC, DOCX, TXT</span> (file size limit 20MB)<br />
          </p>
        </div> : <div className={`p-4 ${themeMode === 'dark' ? 'bg-gray-900 text-white' : ''}`} onClick={(e) => {
          const target = e.target as HTMLElement
          if (target.tagName !== 'BUTTON' && target.parentElement?.tagName !== 'BUTTON') {
            e.stopPropagation()
          }
        }}><div className="flex justify-between items-center mb-5">
            {uploadStatus !== 'idle' && <div className={`font-semibold text-sm ${themeMode === 'dark' ? 'text-white' : 'text-black'}`}>Uploading {fileList.filter(it => it.status === 'uploading').length} files</div>}
            {uploadStatus === 'idle' && <div className={`font-semibold text-sm ${themeMode === 'dark' ? 'text-white' : 'text-black'}`}>{fileList.length} files waiting to upload</div>}
            {uploadStatus === 'idle' && <Button color="primary" variant="solid" shape="round" disabled={fileList.length >= 10 || uploadLoading}>
              <Plus className="size-5"/>
              Add files
            </Button>}
          </div>
          {uploadStatus !== 'idle' && <div className="text-left">
            <span className="font-medium text-function-success">{fileList.filter(it => it.status === 'done').length} files</span>
            {' '}
            uploaded successfully,
            <span className="font-medium text-function-error">{' '}{fileList.filter(it => it.status === 'error').length} files</span>
            {' '}
            encountered errors and failed to upload.
          </div>}
        </div>}
      </Dragger>
  )
}