"use client"
import { get } from 'lodash-es'

import { Modal, Select, Upload, Button } from 'antd'
import { useAtom } from 'jotai'
import { ModalStatusAtom } from './store'
import { Plus, RotateCw } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useMemoizedFn, useRequest } from 'ahooks'
import { prepareFiles } from '../request/prepareFiles'
import { UploadDocumentIcon } from '@/components/icons'
import { toast } from 'sonner'
import './modal.css'
import { UploadDragger } from './UploadDragger'

const { Dragger } = Upload

const UploadWhiteList = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']

export function UploadModal(props: {
  folderList?: Array<{
    folderId: string
    folderName: string
  }>
  defaultFolderId?: string
  onClose?: (dirty: boolean) => void
}) {

  const [modalStatus, setModalStatus] = useAtom(ModalStatusAtom)
  const [fileList, setFileList] = useState<any[]>([])
  const [dirty, setDirty] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<'done' | 'part-error' | 'idle' | 'uploading'>('idle')

  const checkUploadStatus = useMemoizedFn(() => {
    return fileList.every(it => it.status === 'done') ? 'done' : fileList.some(it => it.status === 'error') ? 'part-error' : 'idle'
  })

  const [selectedFolder, setSelectedFolder] = useState<string | undefined>(props.defaultFolderId ?? 'default')

  useEffect(() => {
    if (props.defaultFolderId) {
      setSelectedFolder(props.defaultFolderId)
    }
  }, [props.defaultFolderId])

  const closeModal = useMemoizedFn(() => {
    setModalStatus(prev => {
      return {
        ...prev,
        open: false
      }
    })
    props.onClose?.(dirty)
    setUploadStatus('idle')
    if (fileList.length > 0) {
      setFileList([])
    }
    setDirty(false)
  })

  const { runAsync: uploadFiles, loading: uploadLoading } = useRequest(async () => {
    if (fileList.length === 0) {
      return
    }
    setUploadStatus('uploading')
    setFileList(fileList.map(it => ({
      ...it,
      status: 'uploading',
    })))
    setDirty(true)
    const prepareData = await prepareFiles(fileList, selectedFolder === 'default' ? undefined : selectedFolder)

    if (prepareData.files.length !== fileList.length) {
      setFileList(fileList.map(it => ({
        ...it,
        status: 'error'
      })))
      throw new Error('Upload failed')
    }

    const files = await Promise.all(fileList.map(async (item: any, index: number) => {
      const uploadUrl = get(prepareData, `files[${index}].uploadUrl`, undefined)
      if (!uploadUrl) {
        item.status = 'error'
        // item.error = 'No upload url found'
        setFileList(prev => {
          const index = prev.findIndex(it => it.uid === item.uid)
          if (index !== -1) {
            prev[index] = item
          }
          return [...prev]
        })
        return item
      }
      const response = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': item.mime
        },
        body: item.originFileObj
      })
      const fileRecord = {
        ...item,
        // message: 'Upload failed',
        status: response.ok ? 'done' : 'error',
        uploadUrl,
      }
      console.log('setFileList', fileRecord)
      setFileList(prev => {
        const index = prev.findIndex(it => it.uid === item.uid)
        if (index !== -1) {
          prev[index] = fileRecord
        }
        return [...prev]
      })
      return fileRecord
    }))
    await new Promise(resolve => setTimeout(resolve, 300))
    setUploadStatus(checkUploadStatus())
  }, {
    manual: true
  })


  const {runAsync: retryUpload, loading: retryUploadLoading} = useRequest(async (file: any) => {
    let uploadUrl = file.uploadUrl
    if (!uploadUrl) {
      const data = await prepareFiles([file], selectedFolder === 'default' ? undefined : selectedFolder)
      uploadUrl = get(data, `files[0].uploadUrl`, undefined)
      if (!uploadUrl) {
        throw new Error('No upload url found')
      }
    }
    const response = await fetch(file.uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.mime
      },
      body: file.originFileObj
    })
    if (response.ok) {
      file.status = 'done'
    } else {
      file.status = 'error'
    }
    setFileList(prev => {
      const index = prev.findIndex(it => it.uid === file.uid)
      if (index !== -1) {
        prev[index] = file
      }
      return [...prev]
    })
    await new Promise(resolve => setTimeout(resolve, 300))
    setUploadStatus(checkUploadStatus())
  }, {
    manual: true
  })

  return (
    <Modal
      title="Upload Documents"
      width={'80vw'}
      open={modalStatus.open}
      maskClosable={false}
      onCancel={closeModal}
      classNames={{
        body: fileList.length > 0 ? 'border-b' : '',
      }}
      className='file-upload-modal'
      footer={
        fileList.length > 0 ? <div className="flex justify-center gap-x-4">
          {(uploadStatus === 'idle' || uploadStatus === 'uploading') && <Button color="danger" variant="outlined" onClick={closeModal} disabled={uploadLoading}>
            Discard
          </Button>}
          {(uploadStatus === 'idle' || uploadStatus === 'uploading') && <Button color="primary" variant="solid" onClick={uploadFiles} loading={uploadLoading}>
            Upload
          </Button>}
          {(uploadStatus === 'part-error' || uploadStatus === 'done') && <Button color="danger" variant="outlined" onClick={closeModal} loading={uploadLoading}>
            Close
          </Button>}
          {uploadStatus === 'done' && <Button color="primary" variant="solid" onClick={() => {
            setFileList([])
            setUploadStatus('idle')
          }} loading={uploadLoading}>
            Continue Upload
          </Button>}
        </div>
          : null
      }
    >
      <div className="border-t border-darkbg py-6">
        <div className="flex items-center gap-x-2 mb-6">
          <span className="text-black text-sm font-semibold">Folder:</span>
          <Select
            showSearch={true}
            filterOption={(input, option) => 
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            disabled={uploadStatus === 'part-error' || uploadStatus === 'done' || uploadLoading}
            className="w-[250px]"
            value={selectedFolder}
            onChange={(value) => {
              setSelectedFolder(value)
            }}
            options={[{
              label: 'default',
              value: 'default',
            }].concat(props.folderList?.map(it => ({
              label: it.folderName,
              value: it.folderId,
            })) || [])}
          />
        </div>

        <UploadDragger
          fileList={fileList}
          setFileList={setFileList}
          uploadLoading={uploadLoading}
          retryUploadLoading={retryUploadLoading}
          uploadStatus={uploadStatus}
          retryUpload={retryUpload}
        />
      </div>
    </Modal>
  )
}