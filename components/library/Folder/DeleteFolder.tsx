import React, { useState } from 'react'
import { atom, use<PERSON>tom, useSet<PERSON><PERSON> } from "jotai";
import { Button, Checkbox, Modal } from 'antd';
import { useMemoizedFn, useRequest } from 'ahooks';
import { DeleteWarningIcon } from '@/components/icons';
import { deleteFolder } from '../request/folders';
import { toast } from 'sonner';

export const deleteFolderAtom = atom<{
  id?: string
  open: boolean
  folderName?: string
}>({
  open: false
})


export function DeleteFolderButton(props: {
  folderId: string
  folderName: string
  children: React.ReactElement
}) {
  const setDeleteFolder = useSetAtom(deleteFolderAtom)

  return React.cloneElement(props.children, {
    onClick: () => {
      setDeleteFolder({
        id: props.folderId,
        folderName: props.folderName,
        open: true,
      })
    }
  })
}

export function DeleteFolderModal(props: {
  onConfirm?: () => void
}) {

  const [folderInfo, setFolderInfo] = useAtom(deleteFolderAtom)
  const [isDeleteFiles, setIsDeleteFiles] = useState(true)

  const closeModal = useMemoizedFn(() => {
    setFolderInfo({
      open: false,
      id: undefined,
      folderName: undefined
    })
  })

  const { runAsync: deleteFolderRequest } = useRequest(async () => {
    if (!folderInfo.id) {
      return;
    }
    await deleteFolder(folderInfo.id, isDeleteFiles)
  }, {
    manual: true
  })

  return <Modal
    maskClosable={false}
    centered
    title={`Delete the folder "${folderInfo.folderName}"`}
    open={folderInfo.open}
    onCancel={closeModal}
    footer={
      <div className="flex justify-center gap-x-4 pt-2">
        <Button color="primary" size='large' variant="outlined" onClick={closeModal}>Cancel</Button>
        <Button color="danger" size='large' variant="solid" onClick={async () => {
          try {
            await deleteFolderRequest()
            toast.success('Folder deleted successfully')
            closeModal()
            props.onConfirm?.()
          } catch {
            toast.error('Folder delete failed')
          }
        }}>Delete</Button>
      </div>
    }
    classNames={{
      body: '!py-6 border-b border-t',
      header: '!pb-1.5',
    }}
  >
    <div className='flex flex-col justify-center items-center gap-y-5'>
      <DeleteWarningIcon />
      <p className='text-black font-medium text-sm text-center'>Are you sure you want to delete this folder?<br/>This action cannot be undone. </p>
    </div>
  <div className='flex items-center gap-x-2 mt-4'>
    <Checkbox checked={isDeleteFiles} onChange={(e) => {
      setIsDeleteFiles(e.target.checked)
    }}>Also delete all files inside the folder</Checkbox>
  </div>
  </Modal>
}