"use client"

import { useInterval, useRequest } from "ahooks"
import { getFolderDetail, getFolders } from "./request/folders"
import { UploadButton } from "./Upload/UploadButton";
import { UploadModal } from "./Upload/UploadModal";
import { Share2, Download, Trash, Search, ArrowLeft, FolderTree } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Button, Input, InputRef, Result, Tooltip } from "antd";
import { useRouter } from "next/navigation";
import { get } from "lodash-es";
import { DocumentTable } from "./DocumentTable";
import { DocumentTypeFilter } from "./DocumentTable/documentTypeFilter";
import { useDownloadDocuments, useRefresh, useTableScrollY } from "./DocumentTable/hooks";
import { toast } from "sonner";
import { DeleteDocumentButton, DeleteDocumentModal } from "./DocumentTable/DeleteDocuments";
import { MoveDocumentButton, MoveDocumentModal } from "./DocumentTable/MoveDocuments";
import { PageLoader } from "@/components/ui/page-loader";

export function FolderDetail(props: {
  folderId: string
}) {

  const { refreshFlag, refresh: refreshDocuments } = useRefresh()
  const [typeFilters, setTypeFilters] = useState<string[]>([])
  const [fileName, setFileName] = useState<string>('')
  const fileNameRef = useRef<InputRef>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [dataSource, setDataSource] = useState<any[]>([])
  const router = useRouter()

  const [sort, setSort] = useState<[string, 'descend' | 'ascend'] | []>([])
  const [pagination, setPagination] = useState<{
    current?: number
    pageSize?: number
    total?: number
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const { data, error } = useRequest(() => {
    return getFolderDetail(props.folderId)
  }, {
    refreshDeps: [props.folderId]    
  })
  const { data: { folders } = {}, loading } = useRequest(() => {
    return getFolders()
  }, {
    refreshDeps: [refreshFlag]
  })

  const { folder = {} } = data ?? {}

  const loadNeedLoading = useRef(true)
  const { loading: getDocumentsLoading } = useRequest(async () => {

    const queryString = new URLSearchParams({
      current: pagination.current?.toString() ?? '1',
      pageSize: pagination.pageSize?.toString() ?? '10',
    })
    queryString.append('folder', folder.id)
    if (fileNameRef.current?.input?.value) {
      queryString.append('filename', fileNameRef.current?.input?.value)
    }

    if (sort.length === 2) {
      queryString.append('order', `${sort[0]}_${sort[1]}`)
    }
    if (typeFilters.length > 0) {
      typeFilters.forEach(filter => {
        if (filter === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          queryString.append('fileType', 'application/msword')
        }
        queryString.append('fileType', filter)
      })
    }

    const response = await fetch(`/api/documents?${queryString.toString()}`, {
      method: 'GET',
    })

    const data = await response.json()
    setDataSource(data.documents)
    setPagination({
      current: data.pagination.current,
      pageSize: data.pagination.pageSize,
      total: data.pagination.total,
    })
    loadNeedLoading.current = false
  }, {
    debounceWait: 210,
    ready: !!folder.id,
    refreshDeps: [sort[0], sort[1], typeFilters, pagination.current, pagination.pageSize, fileName, refreshFlag, folder.id],
    // manual: true,
  })

  useEffect(() => {
    loadNeedLoading.current = true
  }, [sort[0], sort[1], typeFilters, pagination.current, pagination.pageSize, fileName, folder.id])


  useEffect(() => {
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    })
  }, [fileName, typeFilters, folder.id])

  const [interval, setInterval] = useState<number | undefined>(undefined)
  const clear = useInterval(() => {
    if (dataSource.every(it => it.status === 'AVAILABLE')) {
      setInterval(undefined)
      clear()
    } else {
      refreshDocuments()
    }
  }, interval)

  const { downloadDocumentsLoading, downloadDocumentsRequest } = useDownloadDocuments()

  useEffect(() => {
    if (fileNameRef.current?.input?.value) {
      setFileName(fileNameRef.current?.input?.value)
    }
  }, [fileNameRef.current?.input?.value])

  const containerRef = useRef<HTMLDivElement>(null)
  const { y }  = useTableScrollY({
    container: containerRef.current,
    offset: 174,
  })

  const checkRowsCanDownload = (keys: string[], datas: any[]) => {
    return keys.every(id => {
      const document = datas.find(doc => doc.id === id)
      return document?.status === 'AVAILABLE'
    })
  }
  const [isLoading, setIsLoading] = useState(false);

  if (error) {
    return <Result
      status="500"
      title="500"
      subTitle="Sorry, something went wrong."
      extra={<Button type="primary">Back Home</Button>}
    />
  }


  return <div className="oveflow-hidden h-full flex flex-col px-[30px] pb-4 bg-[#F3F3F6]">
    <header className="min-h-[70px] flex items-center justify-between border-b border-gray-light mb-6 py-4 libraryHeader">
      <div className="flex items-center gap-x-4">
        <ArrowLeft className="size-6 cursor-pointer" onClick={() => {
          const path = location.pathname.split('/')
          path.pop()
          setIsLoading(true);
          router.replace(path.join('/'))
        }} />
        <h1 className="text-2xl font-medium text-black">{get(folder, 'name')}</h1>
      </div>
      <div className="flex gap-x-3 w-fit uploadBtnLibrary">
        <UploadButton />
      </div>
    </header>
    <div className="flex-1 flex-col bg-white rounded-md px-5 pt-5 py-1 overflow-hidden h-full" ref={containerRef}>
      <div className="flex items-center justify-between mb-5 h-8">
        <div className="flex gap-x-2 w-[300px]">
          <Input
            placeholder="Search documents..."
            ref={fileNameRef}
            className="w-[220px] !bg-[#F3F3F6]"
            variant="borderless"
            prefix={<Search className="size-4 text-gray-medium" />}
            allowClear
            onPressEnter={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onBlur={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onClear={() => {
              setFileName('')
            }}
          />
          <DocumentTypeFilter className="!bg-[#F3F3F6]" typeFilters={typeFilters} setTypeFilters={setTypeFilters} />
        </div>
        <div className="flex gap-x-3 items-center flex-1 justify-end">
          {selectedRowKeys.length > 0 && <>
          <span>{selectedRowKeys.length} selected</span>
          <div className="h-4 w-[1px] bg-gray-light" />
          </>}
          <Button
            color="default"
            variant="filled"
            disabled={selectedRowKeys.length === 0}
            onClick={() => {
              if (!checkRowsCanDownload(selectedRowKeys, dataSource)) {
                toast.error('Some documents are not available to download')
                return
              }
              downloadDocumentsRequest(selectedRowKeys)
            }}
            loading={downloadDocumentsLoading}
          >
            <Download className="size-4" />
           <span className="md:block hidden">Download</span>
          </Button>
          <Tooltip title="Coming soon">
            <Button color="default" variant="filled" disabled>
              <Share2 className="size-4" />
              <span className="md:block hidden">Share</span>
            </Button>
          </Tooltip>
          <div className="h-4 w-[1px] bg-gray-light" />
          <MoveDocumentButton documents={dataSource.filter(it => selectedRowKeys.includes(it.id)).map(it => ({
            documentId: it.id,
            documentName: it.originalName,
            folderId: it.folderId,
            folderName: it.folderName,
          }))}><Button color="default" variant="outlined"
            disabled={selectedRowKeys.length === 0}
          >
            <FolderTree className="size-4" />
            Move Files
          </Button></MoveDocumentButton>
          <DeleteDocumentButton documents={dataSource.filter(it => selectedRowKeys.includes(it.id)).map(it => ({
            documentId: it.id,
            documentName: it.originalName
          }))}><Button color="danger" variant="outlined"
            disabled={selectedRowKeys.length === 0}
          >
            <Trash className="size-4" />
             <span className="md:block hidden">Delete</span>
          </Button></DeleteDocumentButton>
        </div>
      </div>
      <div className="flex-1 overflow-auto">
        <DocumentTable
          tableProps={{
            scroll: {
              y,
            },
          }}
          sort={sort}
          setSort={setSort}
          loading={loadNeedLoading.current && getDocumentsLoading}
          dataSource={dataSource}
          setDataSource={setDataSource}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          pagination={pagination}
          setPagination={setPagination}
        />
      </div>
    </div>
    <UploadModal
      folderList={folders}
      defaultFolderId={folder.id}
      onClose={(dirty) => {
        if (dirty) {
          setInterval(1000 * 5)
          refreshDocuments()
        }
      }}
    />
    <DeleteDocumentModal
      onConfirm={() => {
        refreshDocuments()
        setSelectedRowKeys([])
      }}
    />
    <MoveDocumentModal
      onConfirm={() => {
        refreshDocuments()
        setSelectedRowKeys([])
      }}
    />
    {isLoading && <PageLoader message="Loading..." />}
  </div>
}