"use client"

export const FileView = (props: {
  mime?: string | null,
  downloadUrl?: string | null,
}) => {

  if (!props.mime || !props.downloadUrl) {
    return <div className="h-screen flex items-center justify-center">
      <div className="text-center p-8 bg-gray-50 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-700 mb-2">Unsupported File Type</h2>
        <p className="text-gray-500">Preview not available for this file type.</p>
      </div>
    </div>
  }

  if (props.mime === 'application/pdf') {
    return <div className="h-screen">
      <embed 
        src={`${props.downloadUrl}#toolbar=0&navpanes=0`}
        type="application/pdf"
        width="100%"
        height="100%"
      />
    </div>
  }

  if (props.mime === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || props.mime === 'application/msword') {
    return <div className="h-screen"><iframe src={`https://docs.google.com/gview?url=${encodeURIComponent(props.downloadUrl)}&embedded=true`} width="100%" height="100%"></iframe></div>
  }

  if (props.mime === 'text/plain') {
    return <div className="h-screen max-w-[1000px] mx-auto py-4"><iframe src={props.downloadUrl} width="100%" height="100%"></iframe></div>
  }

  return (
    <div className="h-screen flex items-center justify-center">
      <div className="text-center p-8 bg-gray-50 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-700 mb-2">Unsupported File Type</h2>
        <p className="text-gray-500">Preview not available for this file type.</p>
      </div>
    </div>
  )
}