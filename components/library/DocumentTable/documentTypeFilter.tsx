import { Check, Filter } from "lucide-react"
import { Dropdown } from "antd"
import React from 'react'

export function DocumentTypeFilter(props: {
  className?: string
  typeFilters: string[]
  setTypeFilters: React.Dispatch<React.SetStateAction<string[]>>
  theme?: 'light' | 'dark' | string
}) {

  const { typeFilters, setTypeFilters, theme = 'light' } = props

  return (
    <Dropdown
      arrow
      menu={{
        theme: theme === 'dark' ? 'dark' : 'light',
        className: 'w-[140px] [&>li:not(:last-child)]:border-b [&>li:not(:last-child)]:border-gray-light [&>li]:rounded-none [&>li]:border-dashed [&>li]:!py-2',
        items: [
          {
            label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('application/pdf') ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-black'}`}>
              PDF Files
              {typeFilters.includes('application/pdf') && <Check className="size-4" />}
            </span>,
            key: 'application/pdf',
          },
          {
            label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-black'}`}>
              DOCX Files
              {typeFilters.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') && <Check className="size-4" />}
            </span>,
            key: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          },
          {
            label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('text/plain') ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-black'}`}>
              TXT Files
              {typeFilters.includes('text/plain') && <Check className="size-4" />}
            </span>,
            key: 'text/plain',
          },
        ],
        onClick: ({ key }) => {
          if (typeFilters.includes(key)) {
            setTypeFilters((prev: string[]) => prev.filter((filter) => filter !== key))
          } else {
            setTypeFilters((prev: string[]) => [...prev, key])
          }
        },
      }}
      placement="bottomRight"
    >

      <div className={`flex justify-center items-center size-8 !basis-10 !min-w-[36px] bg-white rounded-md cursor-pointer ${props.className} ${theme === 'dark' ? 'bg-gray-900' : 'bg-white'}`}>
        <Filter className={`size-5 ${typeFilters.length > 0 ? 'text-[#8b5cf6]' : theme === 'dark' ? 'text-white' : 'text-gray-medium'}`} />
      </div>
    </Dropdown>
  )
}