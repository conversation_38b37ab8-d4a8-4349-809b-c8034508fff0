import React from 'react'
import { atom, use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { Button, Modal, Tooltip } from 'antd';
import { useMemoizedFn } from 'ahooks';
import { DeleteWarningIcon } from '@/components/icons';
import { toast } from 'sonner';
import { useDeleteDocuments } from './hooks';

export const deleteDocumentAtom = atom<{
  open: boolean
  documents: Array<{
    documentId: string
    documentName: string
  }>
}>({
  open: false,
  documents: []
})


export function DeleteDocumentButton(props: {
  documents: Array<{
    documentId: string
    documentName: string
  }>
  children: React.ReactElement
}) {
  const setDeleteFolder = useSetAtom(deleteDocumentAtom)

  return React.cloneElement(props.children, {
    onClick: () => {
      setDeleteFolder({
        documents: props.documents,
        open: true,
      })
    }
  })
}

export function DeleteDocumentModal(props: {
  onConfirm?: () => void
}) {

  const [documentInfo, setDocumentInfo] = useAtom(deleteDocumentAtom)

  const closeModal = useMemoizedFn(() => {
    setDocumentInfo({
      open: false,
      documents: [],
    })
  })
  const { deleteDocumentsLoading, deleteDocumentsRequest } = useDeleteDocuments()

  const getDisplayNames = (documents: typeof documentInfo.documents) => {
    if (documents.length <= 2) {
      return documents.map(it => it.documentName).join(', ');
    }
    return `${documents[0].documentName}, ${documents[1].documentName} and ${documents.length - 2} more`;
  };

  const getTooltipContent = (documents: typeof documentInfo.documents) => {
    return documents.map(it => it.documentName).join(', ');
  };

  return <Modal
    maskClosable={false}
    centered
    title={`Delete the document(s)`}
    open={documentInfo.open}
    onCancel={closeModal}
    footer={
      <div className="flex justify-center gap-x-4 pt-2">
        <Button color="primary" size='large' variant="outlined" onClick={closeModal}>Cancel</Button>
        <Button color="danger" size='large' variant="solid" loading={deleteDocumentsLoading} onClick={async () => {
          try {
            await deleteDocumentsRequest(documentInfo.documents.map(it => it.documentId))
            toast.success(`Delete ${documentInfo.documents.length > 1 ? 'documents' : 'document'} successfully`)
            closeModal()
            props.onConfirm?.()
          } catch {
            toast.error('Delete documents failed')
          }
        }}>Delete</Button>
      </div>
    }
    classNames={{
      body: '!py-6 border-b border-t',
      header: '!pb-1.5',
    }}
  >
    <div className='flex flex-col justify-center items-center gap-y-5'>
      <DeleteWarningIcon />
      <p className='text-black font-medium text-sm text-center'>
        Are you sure you want to delete {documentInfo.documents.length > 1 ? documentInfo.documents.length : 'this'} document{documentInfo.documents.length > 1 ? 's' : ''}?<br/>
        <Tooltip 
          title={getTooltipContent(documentInfo.documents)} 
          placement="bottom"
          classNames={{
            root: '!max-w-[500px]'
          }}
        >
          <span className='text-function-error'>{getDisplayNames(documentInfo.documents)}</span>
        </Tooltip><br/>
        This action cannot be undone. 
      </p>
    </div>
  </Modal>
}