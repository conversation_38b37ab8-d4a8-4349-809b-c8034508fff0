import React from 'react'
import { atom, useAtom, useSet<PERSON>tom } from "jotai";
import { Button, Modal, Select, Tooltip } from 'antd';
import { useMemoizedFn, useRequest } from 'ahooks';
import { useMoveDocuments } from './hooks';
import { getFolders } from '../request/folders';
import { toast } from 'sonner';

export const moveDocumentAtom = atom<{
  open: boolean
  documents: Array<{
    documentId: string
    documentName: string
    folderId: string
    folderName: string
  }>
  targetFolderId?: string
}>({
  open: false,
  documents: []
})


export function MoveDocumentButton(props: {
  documents: Array<{
    documentId: string
    documentName: string
    folderId: string
    folderName: string
  }>
  children: React.ReactElement
}) {
  const setMoveFolder = useSetAtom(moveDocumentAtom)

  return React.cloneElement(props.children, {
    onClick: () => {
      setMoveFolder({
        documents: props.documents,
        open: true,
        targetFolderId: undefined
      })
    }
  })
}

export function MoveDocumentModal(props: {
  onConfirm?: () => void
}) {

  const [documentInfo, setDocumentInfo] = useAtom(moveDocumentAtom)

  const closeModal = useMemoizedFn(() => {
    setDocumentInfo({
      open: false,
      documents: [],
      targetFolderId: undefined,
    })
  })
  const { moveDocumentsLoading, moveDocumentsRequest } = useMoveDocuments()

  const getDisplayNames = (documents: typeof documentInfo.documents) => {
    if (documents.length <= 2) {
      return documents.map(it => it.documentName).join(', ');
    }
    return `${documents[0].documentName}, ${documents[1].documentName} and ${documents.length - 2} more`;
  };

  const getTooltipContent = (documents: typeof documentInfo.documents) => {
    return documents.map(it => it.documentName).join(', ');
  };

  const { data: { folders = [], rootFolderId } = {} } = useRequest(() => {
    return getFolders()
  }, {
    refreshDeps: [documentInfo.open]
  })

  return <Modal
    maskClosable={false}
    centered
    title={`Move the document(s)`}
    open={documentInfo.open}
    onCancel={closeModal}
    footer={
      <div className="flex justify-center gap-x-4 pt-2">
        <Button color="primary" size='large' variant="outlined" onClick={closeModal}>Cancel</Button>
        <Button color="danger" size='large' variant="solid" disabled={!documentInfo.targetFolderId} loading={moveDocumentsLoading} onClick={async () => {
          if (!documentInfo.targetFolderId) {
            toast.error('Please select a folder to move to')
            return
          }
          try {
            await moveDocumentsRequest(documentInfo.documents.map(it => it.documentId), documentInfo.targetFolderId)
            closeModal()
            props.onConfirm?.()
          } catch {
          }
        }}>Move</Button>
      </div>
    }
    classNames={{
      body: '!py-6 border-b border-t',
      header: '!pb-1.5',
    }}
  >
    <div className='flex flex-col justify-center gap-y-5'>
      <div>
        Are you sure you want to move {documentInfo.documents.length > 1 ? documentInfo.documents.length : 'this'} document{documentInfo.documents.length > 1 ? 's' : ''}?<br/>
        <Tooltip 
          title={getTooltipContent(documentInfo.documents)} 
          placement="bottom"
          classNames={{
            root: '!max-w-[500px]'
          }}
        >
          <span className='text-function-error'>{getDisplayNames(documentInfo.documents)}</span>
        </Tooltip>
      </div>
      <div>
        Will Move to Folder: <Select
          showSearch={true}
          filterOption={(input, option) => 
            (option?.folderName ?? '').toLowerCase().includes(input.toLowerCase())
          }
          className={`w-[220px]`}
          options={[{
            folderName: 'default',
            folderId: rootFolderId,
          }, ...folders ]}
          fieldNames={{
            label: 'folderName',
            value: 'folderId',
          }}
          onChange={(value) => {
            setDocumentInfo(prev => ({
              ...prev,
              targetFolderId: value
            }))
          }}
          value={documentInfo.targetFolderId}
        ></Select>
      </div>
    </div>
  </Modal>
}