import { useState, useEffect } from "react";
import { Shield, CheckCircle, AlertCircle, ExternalLink } from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import type { ExtendedMessage } from "@/lib/types";
import { useRouter, usePathname } from "next/navigation";
import { Logger } from "@/lib/utils/Logger";
import { FeatureFeedback } from "./feature-feedback";

interface Citation {
  id: string;
  citation: string;
  url?: string;
  status: "Valid" | "Need Review";
  details: string;
}

interface CitationValidationResult {
  citations: Citation[];
  overallAssessment: string;
  validCitationsCount: number;
  needReviewCount: number;
  rawResponse?: string;
}

interface CitationVerificationDisplayProps {
  message: ExtendedMessage;
}

export function CitationVerificationDisplay({ message }: CitationVerificationDisplayProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCitation, setSelectedCitation] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<CitationValidationResult | null>(null);
  
  // Add effect to select the first citation when validation result is loaded
  useEffect(() => {
    if (validationResult && validationResult?.citations?.length > 0 && !selectedCitation) {
      setSelectedCitation(validationResult.citations[0].id);
    }
  }, [validationResult, selectedCitation]);
  
  // Get chatId from URL
  const pathname = usePathname();
  const chatId = pathname?.split('/').pop() || '';
  
  // Local storage key for caching validation results
  const localStorageKey = `citation-validation-${message.id}`;
  
  // Load validation data when dialog opens
  useEffect(() => {
    if (isOpen) {
      // First check if we already have validation data in the message metadata
      if (message.metadata?.citationValidation) {
        try {
          const validationData = typeof message.metadata.citationValidation === 'string' 
            ? JSON.parse(message.metadata.citationValidation) 
            : message.metadata.citationValidation;
          
          setValidationResult(validationData);
          setIsLoading(false);
          
          // If we have data in the message metadata, we can remove it from localStorage
          // as the metadata is the source of truth
          localStorage.removeItem(localStorageKey);
          return;
        } catch (error) {
          Logger.error('Error parsing citation validation data from metadata:', error);
          // Continue to next check if parsing fails
        }
      }
      
      // Next, check localStorage for cached validation data
      try {
        const cachedData = localStorage.getItem(localStorageKey);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          setValidationResult(parsedData);
          setIsLoading(false);
          return;
        }
      } catch (error) {
        Logger.error('Error retrieving cached citation validation data:', error);
        // Continue to API call if localStorage retrieval fails
      }
      
      // If no cached data found, fetch from API
      fetchValidationData();
    }
  }, [isOpen, message.id, chatId, localStorageKey]);
  
  const fetchValidationData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/citation-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messageId: message.id,
          chatId: chatId,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch citation validation data');
      }
      
      const data = await response.json();
      setValidationResult(data.validationResult);
      
      // Cache the result in localStorage
      localStorage.setItem(localStorageKey, JSON.stringify(data.validationResult));
    } catch (error) {
      Logger.error('Error fetching citation validation data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Only show the button for assistant messages
  if (message.role !== 'assistant') {
    return null;
  }

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1 ml-2" 
            onClick={() => setIsOpen(true)}
          >
            <Shield className="h-4 w-4" />
            <span>Cite Check</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          Verify citation accuracy
        </TooltipContent>
      </Tooltip>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle>Citation Verification Report</DialogTitle>
            <div className="flex items-center gap-2">
              <FeatureFeedback
                featureType="cite-check"
                title="Feedback on Citation Check"
                metadata={{
                  chatId,
                  messageId: message.id,
                  validationStats: validationResult ? {
                    totalCitations: validationResult.citations.length,
                    validCount: validationResult.validCitationsCount,
                    needReviewCount: validationResult.needReviewCount
                  } : null
                }}
                buttonText="Share your feedback"
                className="mr-2"
              />
              <DialogClose />
            </div>
          </DialogHeader>

          {isLoading ? (
            <div className="flex items-center justify-center h-[400px]">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-muted-foreground">Analyzing citations...</p>
              </div>
            </div>
          ) : validationResult ? (
            <div className="grid grid-cols-3 gap-6 overflow-hidden h-[70vh]">
              {/* Left column (2/3): Stats and Table */}
              <div className="col-span-2 flex flex-col gap-4 overflow-hidden">
                {/* Stats cards */}
                <div className="grid grid-cols-3 gap-4">
                  <Card className="p-4 flex flex-col items-center justify-center">
                    <span className="text-3xl font-bold">{validationResult.citations.length}</span>
                    <span className="text-sm text-muted-foreground">Total Cites</span>
                  </Card>
                  <Card className="p-4 flex flex-col items-center justify-center">
                    <span className="text-3xl font-bold text-green-500">{validationResult.validCitationsCount}</span>
                    <span className="text-sm text-muted-foreground">Valid</span>
                  </Card>
                  <Card className="p-4 flex flex-col items-center justify-center">
                    <span className="text-3xl font-bold text-amber-500">{validationResult.needReviewCount}</span>
                    <span className="text-sm text-muted-foreground">Need Review</span>
                  </Card>
                </div>

                {/* Citation table */}
                <div className="overflow-y-auto flex-1 border rounded-md">
                  <table className="w-full">
                    <thead className="border-b sticky top-0 bg-background z-10">
                      <tr>
                        <th className="text-left py-2 px-4 font-medium">Citation</th>
                        <th className="text-center py-2 px-4 font-medium w-32">Status</th>
                        {/* <th className="text-left py-2 px-4 font-medium">Details</th> */}
                      </tr>
                    </thead>
                    <tbody>
                      {validationResult.citations.map((cite) => (
                        <tr 
                          key={cite.id} 
                          className={cn(
                            "border-b cursor-pointer hover:bg-muted/50 transition-colors",
                            selectedCitation === cite.id && "bg-muted font-medium border-l-2 border-l-primary"
                          )}
                          onClick={() => setSelectedCitation(cite.id)}
                        >
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-2">
                              {cite.status === "Valid" ? (
                                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-amber-500 flex-shrink-0" />
                              )}
                              <div className="flex items-center gap-1">
                                <span>{typeof cite.citation === 'string' ? cite.citation : JSON.stringify(cite.citation)}</span>
                                {cite.url && (
                                  <a 
                                    href={cite.url} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <ExternalLink className="h-3 w-3 text-muted-foreground" />
                                  </a>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-center">
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs font-medium",
                              cite.status === "Valid" 
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" 
                                : "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                            )}>
                              {cite.status}
                            </span>
                          </td>
                          {/* <td className="py-3 px-4">
                            <p className="line-clamp-2 text-sm">{cite.details}</p>
                          </td> */}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              {/* Right column (1/3): Citation details and Overall assessment */}
              <div className="col-span-1 flex flex-col gap-4 overflow-hidden">
                {/* Selected citation details */}
                {selectedCitation && (
                  <div className="p-4 bg-muted/30 rounded-md border overflow-y-auto max-h-[40vh]">
                    {(() => {
                      const cite = validationResult.citations.find(c => c.id === selectedCitation);
                      if (!cite) return null;
                      
                      return (
                        <>
                          <div className="flex items-center gap-2 mb-2">
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs font-medium uppercase",
                              cite.status === "Valid" 
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" 
                                : "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                            )}>
                              {cite.status}
                            </span>
                            <h4 className="font-medium">Citation Details</h4>
                          </div>
                          <div className="mb-2">
                            {/* <p className="text-sm font-medium">Citation Text:</p> */}
                            <p className="text-sm">{typeof cite.citation === 'string' ? cite.citation : JSON.stringify(cite.citation)}</p>
                          </div>
                          
                          {cite.url && (
                            <div className="mb-2">
                              <a 
                                href={cite.url} 
                                target="_blank" 
                                rel="noopener noreferrer" 
                                className="text-sm text-blue-500 hover:underline flex items-center gap-1 w-fit"
                              >
                                <span>View Source</span>
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                          )}
                          
                          <div>
                            <p className="text-sm font-medium">Assessment:</p>
                            <p className="text-sm">{typeof cite.details === 'string' ? cite.details : JSON.stringify(cite.details)}</p>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                )}
                
                {/* Overall assessment */}
                <div className="p-4 bg-muted/30 rounded-md border overflow-y-auto flex-1">
                  <h4 className="font-medium mb-2">Overall Assessment</h4>
                  <p className="text-sm whitespace-pre-wrap">{typeof validationResult.overallAssessment === 'string' 
                    ? validationResult.overallAssessment 
                    : JSON.stringify(validationResult.overallAssessment)}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-[400px]">
              <div className="flex flex-col items-center gap-2">
                <p className="text-muted-foreground">No citation data available</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}


