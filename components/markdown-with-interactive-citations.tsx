'use client';

import React, { memo, useEffect } from 'react';
import ReactMarkdown, { type Components } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkSmartypants from 'remark-smartypants';
import remarkBreaks from 'remark-breaks';
import Link from 'next/link';
import { CitationMarker } from './citation-marker';
import { CitationMatch } from '@/lib/utils/citation-formatter';
import { CitationTextProcessor } from '@/lib/utils/citation-text-processor';

interface MarkdownWithInteractiveCitationsProps {
  children: string;
  citations?: CitationMatch[];
  role?: string;
}

/**
 * Creates a custom text renderer that processes citation markers inline
 */
function createInlineTextRenderer(citations: CitationMatch[], originalContent: string) {
  // Create citation map for quick lookup - ensure proper indexing
  const citationMap = new Map<number, CitationMatch>();
  citations.forEach((citation, index) => {
    citationMap.set(index + 1, citation);
  });

  // Debug logging for citation mapping (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log("🗺️ Citation map created:", {
      totalCitations: citations.length,
      citationNumbers: Array.from(citationMap.keys()),
      citationData: citations.map((c, i) => ({
        index: i + 1,
        filename: c.filename,
        pageNumber: c.pageNumber,
        hasPreview: !!c.chunkPreview,
        chunkId: c.chunkId,
        chunkText: c.chunkText?.substring(0, 50) + '...',
        similarity: c.similarity
      })),
      citationMapEntries: Array.from(citationMap.entries()).map(([num, citation]) => ({
        number: num,
        filename: citation.filename,
        pageNumber: citation.pageNumber
      }))
    });
  }

  return function InlineTextRenderer({ children }: { children: string }) {
    if (!children || typeof children !== 'string') {
      return <>{children}</>;
    }

    // Check if this text contains citation markers (now looking for [1], [2], [3] format)
    const citationRegex = /\[(\d+)\]/g;
    const hasCitationMarkers = citationRegex.test(children);

    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 InlineTextRenderer processing:", {
        textLength: children.length,
        textContent: children.substring(0, 100) + (children.length > 100 ? '...' : ''),
        hasCitationMarkers,
        citationMapSize: citationMap.size
      });
    }

    if (!hasCitationMarkers) {
      return <>{children}</>;
    }

    // Debug logging for text processing (only in development)
    if (process.env.NODE_ENV === 'development') {
      const citationMatches = Array.from(children.matchAll(/\[(\d+)\]/g));
      console.log("Processing text with citations:", {
        textLength: children.length,
        citationMatches: citationMatches.map(m => ({
          marker: m[0],
          number: parseInt(m[1], 10),
          position: m.index
        })),
        availableCitations: Array.from(citationMap.keys())
      });
    }

    // Split text and create inline elements
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let partKey = 0;

    // Reset regex for processing
    const processingRegex = /\[(\d+)\]/g;
    let match;

    while ((match = processingRegex.exec(children)) !== null) {
      const citationNumber = parseInt(match[1], 10);
      const citation = citationMap.get(citationNumber);

      // Debug logging for each citation lookup (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 Processing citation marker:", {
          marker: match[0],
          citationNumber,
          foundCitation: !!citation,
          availableCitationNumbers: Array.from(citationMap.keys()),
          citationMapSize: citationMap.size,
          citationData: citation ? {
            filename: citation.filename,
            pageNumber: citation.pageNumber,
            hasPreview: !!citation.chunkPreview,
            chunkId: citation.chunkId,
            chunkText: citation.chunkText?.substring(0, 50) + '...'
          } : null,
          lookupResult: citationMap.has(citationNumber) ? 'FOUND' : 'NOT_FOUND'
        });
      }

      // Add text before citation (as plain text to maintain inline flow)
      if (match.index > lastIndex) {
        const textBefore = children.substring(lastIndex, match.index);
        if (textBefore) {
          parts.push(textBefore);
        }
      }

      // Always render CitationMarker component - it will handle missing citation data internally
      const referenceText = CitationTextProcessor.extractReferenceText(originalContent, citationNumber);

      if (process.env.NODE_ENV === 'development') {
        console.log("🔗 Creating CitationMarker:", {
          citationNumber,
          hasCitation: !!citation,
          hasReferenceText: !!referenceText,
          partKey
        });
      }

      parts.push(
        <CitationMarker
          key={`citation-${citationNumber}-${partKey++}`}
          citationNumber={citationNumber}
          citation={citation}
          referenceText={referenceText || undefined}
        />
      );

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after the last citation
    if (lastIndex < children.length) {
      const remainingText = children.substring(lastIndex);
      if (remainingText) {
        parts.push(remainingText);
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log("🎯 InlineTextRenderer final result:", {
        totalParts: parts.length,
        partTypes: parts.map(part => typeof part === 'string' ? 'text' : 'component'),
        textLength: children.length
      });
    }

    return <>{parts}</>;
  };
}

/**
 * Creates markdown components with inline citation support
 */
function createMarkdownComponents(citations: CitationMatch[], originalContent: string): Partial<Components> {
  const InlineTextRenderer = createInlineTextRenderer(citations, originalContent);

  return {
    // Override text rendering to handle citations inline
    text: ({ children }) => {
      if (typeof children === 'string') {
        return <InlineTextRenderer>{children}</InlineTextRenderer>;
      }
      return <>{children}</>;
    },

    // Standard markdown components with proper styling
    p: ({ children, ...props }) => (
      <p className="my-6 [li_&]:my-3 [li_&>strong]:inline-block" {...props}>
        {children}
      </p>
    ),
    ol: ({ children, ...props }) => (
      <ol className="list-decimal list-outside ml-4 mt-2" {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }) => (
      <li className="py-1 [&>p]:my-3" {...props}>
        {children}
      </li>
    ),
    ul: ({ children, ...props }) => (
      <ul className="list-disc list-outside ml-4 mt-2" {...props}>
        {children}
      </ul>
    ),
    strong: ({ children, ...props }) => (
      <strong className="font-bold" {...props}>{children}</strong>
    ),
    a: ({ children, ...props }) => {
      // Ensure href is defined for Next.js Link component
      if (!props.href) {
        return <span className="text-blue-500">{children}</span>;
      }
      return (
        <Link
          className="text-blue-500 hover:underline"
          target="_blank"
          rel="noreferrer"
          {...props}
          href={props.href}
        >
          {children}
        </Link>
      );
    },
    h1: ({ children, ...props }) => (
      <h1 className="text-base font-bold mt-6 mb-2" {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }) => (
      <h2 className="text-base font-bold mt-6 mb-2" {...props}>
        {children}
      </h2>
    ),
    h3: ({ children, ...props }) => (
      <h3 className="text-base font-bold mt-6 mb-2" {...props}>
        {children}
      </h3>
    ),
    h4: ({ children, ...props }) => (
      <h4 className="text-base font-bold mt-6 mb-2" {...props}>
        {children}
      </h4>
    ),
    h5: ({ children, ...props }) => (
      <h5 className="text-base font-bold mt-6 mb-2" {...props}>
        {children}
      </h5>
    ),
    h6: ({ children, ...props }) => (
      <h6 className="text-base font-bold mt-6 mb-2" {...props}>
        {children}
      </h6>
    ),
    hr: ({ ...props }) => <div className="my-4" {...props} />,
    blockquote: ({ children, ...props }) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 italic my-4" {...props}>
        {children}
      </blockquote>
    ),
    code: ({ children, className, ...props }) => {
      const isInline = !className;
      if (isInline) {
        return (
          <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm" {...props}>
            {children}
          </code>
        );
      }
      return <code {...props}>{children}</code>;
    },
    pre: ({ children, ...props }) => (
      <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-4" {...props}>
        {children}
      </pre>
    ),
  };
}

const remarkPlugins = [remarkGfm, remarkSmartypants, remarkBreaks];

/**
 * Direct content processing that replaces citation markers with interactive components
 * This approach bypasses ReactMarkdown's text component override issues
 */
function processContentWithDirectCitationReplacement(content: string, citations: CitationMatch[]): React.ReactNode {
  // Create citation map for quick lookup
  const citationMap = new Map<number, CitationMatch>();
  citations.forEach((citation, index) => {
    citationMap.set(index + 1, citation);
  });

  if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Direct content processing:", {
      contentLength: content.length,
      citationMapSize: citationMap.size,
      contentPreview: content.substring(0, 200) + '...',
      hasCitationMarkers: /\[(\d+)\]/.test(content)
    });
  }

  // Check if content has citation markers
  const citationRegex = /\*\*([1-9]|10)\*\*/;
  if (!citationRegex.test(content)) {
    // No citations, render as normal markdown
    return (
      <ReactMarkdown remarkPlugins={remarkPlugins}>{content}</ReactMarkdown>
    );
  }

  const components: Components = {
    strong: ({ children }) => {
      const text = typeof children === 'string' ? children : undefined;

      if (text && !isNaN(parseInt(text))) {
        const citation = citationMap.get(parseInt(text));
        return (
          <CitationMarker
            key={`citation-${parseInt(text)}`}
            citationNumber={parseInt(text)}
            citation={citation}
          />
        );
      }
      // Ensure children is rendered correctly
      return <strong> {children}</strong>;
    },
    
    
  };

  return (
    <ReactMarkdown
      remarkPlugins={remarkPlugins}
      components={components}
    >
      {content}
    </ReactMarkdown>
  );

}

/**
 * Enhanced markdown components that handle inline citations properly
 */
function createInlineCitationComponents(citations: CitationMatch[]) {
  // Create citation map for quick lookup
  const citationMap = new Map<number, CitationMatch>();
  citations.forEach((citation, index) => {
    citationMap.set(index + 1, citation);
  });

  return {
    // Custom text renderer that processes citation markers inline
    text: ({ children }: { children: string }) => {
      const text = children;

      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 Text component called with:", {
          textLength: text.length,
          textContent: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
          hasCitationMarkers: /\[(\d+)\]/.test(text),
          hasOldMarkers: /\[\^(\d+)\]/.test(text)
        });
      }

      // Check if this text contains citation markers
      const citationRegex = /\[(\d+)\]/g;
      if (!citationRegex.test(text)) {
        return <>{text}</>;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 Processing text node with citations:", {
          textLength: text.length,
          textContent: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
          citationMapSize: citationMap.size
        });
      }

      // Process text to replace citation markers with components
      const parts: React.ReactNode[] = [];
      let lastIndex = 0;
      let match;
      let partKey = 0;

      // Reset regex and process matches
      citationRegex.lastIndex = 0;
      while ((match = citationRegex.exec(text)) !== null) {
        const citationNumber = parseInt(match[1], 10);
        const citation = citationMap.get(citationNumber);

        if (process.env.NODE_ENV === 'development') {
          console.log("🔗 Found citation in text:", {
            marker: match[0],
            citationNumber,
            hasCitation: !!citation,
            position: match.index
          });
        }

        // Add text before citation
        if (match.index > lastIndex) {
          const textBefore = text.substring(lastIndex, match.index);
          if (textBefore) {
            parts.push(textBefore);
          }
        }

        // Add citation marker component (inline)
        parts.push(
          <CitationMarker
            key={`text-citation-${citationNumber}-${partKey++}`}
            citationNumber={citationNumber}
            citation={citation}
          />
        );

        lastIndex = match.index + match[0].length;
      }

      // Add remaining text
      if (lastIndex < text.length) {
        const remainingText = text.substring(lastIndex);
        if (remainingText) {
          parts.push(remainingText);
        }
      }

      return <>{parts}</>;
    }
  };
}

const NonMemoizedMarkdownWithInteractiveCitations = ({
  children,
  citations = [],
  role = "assistant"
}: MarkdownWithInteractiveCitationsProps) => {
  // Debug logging for citation data flow (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("📝 MarkdownWithInteractiveCitations rendered:", {
        role,
        contentLength: children.length,
        citationCount: citations.length,
        hasCitationMarkers: CitationTextProcessor.hasCitations(children),
        contentPreview: children.substring(0, 200) + '...',
        citations: citations.map((c, i) => ({
          index: i + 1,
          filename: c.filename,
          pageNumber: c.pageNumber,
          chunkId: c.chunkId,
          hasPreview: !!c.chunkPreview,
          previewLength: c.chunkPreview?.length || 0,
          fullCitation: c
        }))
      });

      // Check for citation markers specifically (both old and new formats)
      const oldMarkers = children.match(/\[\^(\d+)\]/g) || [];
      const newMarkers = processedContent.match(/\[(\d+)\]/g) || [];
      console.log("🎯 Markdown citation markers:", {
        oldMarkersFound: oldMarkers.length,
        oldMarkers: oldMarkers,
        newMarkersFound: newMarkers.length,
        newMarkers: newMarkers,
        citationsAvailable: citations.length,
        contentTransformed: oldMarkers.length !== newMarkers.length
      });
    }
  }, [children, citations, role]);

  // Remove the References section since we're using interactive tooltips
  const contentWithoutReferences = CitationTextProcessor.removeReferencesSection(children);

  // Pre-process content to replace citation markers with simple [1], [2], [3] format
  const processedContent = contentWithoutReferences.replace(/\[\^(\d+)\]/g, (match, number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log("🔄 Converting citation marker:", { original: match, converted: `[${number}]` });
    }
    return `[${number}]`;
  });

  if (process.env.NODE_ENV === 'development') {
    console.log("📝 Content processing result:", {
      originalLength: contentWithoutReferences.length,
      processedLength: processedContent.length,
      originalMarkers: (contentWithoutReferences.match(/\[\^(\d+)\]/g) || []).length,
      processedMarkers: (processedContent.match(/\[(\d+)\]/g) || []).length,
      contentChanged: contentWithoutReferences !== processedContent,
      originalPreview: contentWithoutReferences.substring(0, 200) + '...',
      processedPreview: processedContent.substring(0, 200) + '...'
    });
  }

  // If no citations, render normal markdown
  if (!citations || citations.length === 0) {
    if (process.env.NODE_ENV === 'development') {
      console.log("No citations provided, rendering normal markdown");
    }
    return (
      <div className={`markdown-content ${role === "user" ? "[&>p]:mt-0" : ""}`}>
        <ReactMarkdown remarkPlugins={remarkPlugins}>
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }

  // Check if content actually has citation markers (check both old and new formats)
  const hasOldMarkers =  /\*\*([1-9]|10)\*\*/.test(contentWithoutReferences);
  const hasNewMarkers =  /\*\*([1-9]|10)\*\*/.test(processedContent);

  if (!hasOldMarkers && !hasNewMarkers) {
    if (process.env.NODE_ENV === 'development') {
      console.log("No citation markers found in content, rendering normal markdown");
    }
    return (
      <div className={`markdown-content ${role === "user" ? "[&>p]:mt-0" : ""}`}>
        <ReactMarkdown remarkPlugins={remarkPlugins}>
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }

  if (process.env.NODE_ENV === 'development') {
    console.log("Rendering with interactive citations");
  }

  // Create custom components with inline citation support
  const inlineCitationComponents = createInlineCitationComponents(citations);

  // Try a more direct approach - process the content completely before ReactMarkdown
  const finalContent = processContentWithDirectCitationReplacement(processedContent, citations);

  // Render with interactive citations
  return (
    <div className={`markdown-content ${role === "user" ? "[&>p]:mt-0" : ""}`}>
      {finalContent}
    </div>
  );
};

export const MarkdownWithInteractiveCitations = memo(
  NonMemoizedMarkdownWithInteractiveCitations,
  (prevProps, nextProps) => {
    const isEqual = prevProps.children === nextProps.children &&
      prevProps.role === nextProps.role &&
      prevProps.citations === nextProps.citations;

    if (process.env.NODE_ENV === 'development') {
      console.log("🔄 MarkdownWithInteractiveCitations memo check:", {
        isEqual,
        childrenChanged: prevProps.children !== nextProps.children,
        roleChanged: prevProps.role !== nextProps.role,
        citationsChanged: prevProps.citations !== nextProps.citations,
        prevCitationsLength: prevProps.citations?.length || 0,
        nextCitationsLength: nextProps.citations?.length || 0
      });
    }

    return isEqual;
  }
);
