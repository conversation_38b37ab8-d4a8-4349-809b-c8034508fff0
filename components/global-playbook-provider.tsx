"use client";

import React, { createContext, useState, useContext, useEffect } from "react";
import { PlaybookDrawer } from "./starter-pack";
import { Button } from "./ui/button";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "./ui/tooltip";
import { Library, ChevronLeftIcon } from "lucide-react";
import { logEvent } from "@/lib/analytics/events-client";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";
import { useUser } from '@/contexts/UserContext';
import { usePathname } from 'next/navigation';

type PlaybookContextType = {
  openPlaybook: () => void;
  closePlaybook: () => void;
  isPlaybookOpen: boolean;
};

const PlaybookContext = createContext<PlaybookContextType | undefined>(undefined);

export function usePlaybook() {
  const context = useContext(PlaybookContext);
  if (!context) {
    throw new Error("usePlaybook must be used within a GlobalPlaybookProvider");
  }
  return context;
}

export function GlobalPlaybookProvider({ children }: { children: React.ReactNode }) {
  const [isPlaybookOpen, setPlaybookOpen] = useState(false);
  const [headerHeight, setHeaderHeight] = useState(60); // Default header height
  const user = useUser();
  const [isLimited, setIsLimited] = useState<string | null>(null);
  const pathname = usePathname();

  const isPricingPage = pathname === '/subscription';

  // Initialize client-side state
  useEffect(() => {
    setIsLimited(localStorage.getItem('isLimited'));
  }, []);

  // Detect the actual header height and update on window resize
  useEffect(() => {
    function updateHeaderHeight() {
      const header = document.querySelector('header');
      if (header) {
        const height = header.getBoundingClientRect().height;
        setHeaderHeight(height);
      }
    }

    // Initial measurement
    updateHeaderHeight();

    // Add resize listener to handle dynamic changes
    window.addEventListener('resize', updateHeaderHeight);

    // Create a MutationObserver to detect DOM changes that might affect header height
    const observer = new MutationObserver(updateHeaderHeight);
    const header = document.querySelector('header');
    if (header) {
      observer.observe(header, { attributes: true, childList: true, subtree: true });
    }

    return () => {
      window.removeEventListener('resize', updateHeaderHeight);
      observer.disconnect();
    };
  }, []);

  // Add effect to hide utility buttons when playbook is open
  useEffect(() => {
    const utilityButtons = document.querySelector('.fixed.bottom-4.right-4.z-50');
    if (utilityButtons) {
      if (isPlaybookOpen) {
        utilityButtons.classList.add('opacity-0', 'pointer-events-none');
        utilityButtons.classList.remove('opacity-100');
      } else {
        utilityButtons.classList.remove('opacity-0', 'pointer-events-none');
        utilityButtons.classList.add('opacity-100');
      }
    }
  }, [isPlaybookOpen]);

  // Use an effect for content shifting limited to specific elements
  useEffect(() => {
    if (typeof document !== 'undefined') {
      // Target multiple potential content elements to find the right one
      const potentialElements = [
        '.main-chat-section',
        '.chat-container',
        'main',
        '.chat-messages-container',
        '[data-sidebar="inset"]',
        '.SidebarInset'
      ];
      let contentElement = null;
      for (const selector of potentialElements) {
        const element = document.querySelector(selector);
        if (element) {
          contentElement = element;
          break;
        }
      }
      // If we found a content element, apply the class
      if (contentElement) {
        if (isPlaybookOpen) {
          contentElement.classList.add('playbook-open');
        } else {
          contentElement.classList.remove('playbook-open');
        }
      }
    }
  }, [isPlaybookOpen]);

  const openPlaybook = () => {
    // Log the Playbook drawer open event with user ID
    logEvent(SiteBehaviorEvent.PLAYBOOK_FEATURE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      step: "opened playbook drawer",
      stepNumber: "1",
    });

    setPlaybookOpen(true);
  };

  const closePlaybook = () => setPlaybookOpen(false);

  // Get the state string for data-state attribute
  const state = isPlaybookOpen ? "expanded" : "collapsed";

  return (
    <PlaybookContext.Provider value={{ openPlaybook, closePlaybook, isPlaybookOpen }}>
      {children}

      {!isPricingPage || !isLimited && (
        <div className="fixed top-4 right-4 z-[89]">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                className="shrink-0 md:h-fit flex items-center gap-1 ml-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white p-2 shadow-md transition-all duration-200"
                onClick={() => isPlaybookOpen ? closePlaybook() : openPlaybook()}
              >
                <Library size={16} />
                <span className="hidden md:inline font-medium">Iqidis Playbook</span>
                {isPlaybookOpen ? (
                  <ChevronLeftIcon size={14} className="rotate-180" />
                ) : (
                  <ChevronLeftIcon size={14} />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isPlaybookOpen ? "Close Playbook" : "Open Playbook"}
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      {/* Playbook Drawer with overlay (no content shifting) */}
      <div
        className="fixed sm:right-0 right-auto sm:left-auto left-0 z-[100] h-full drawerMobile pointer-events-none"
        data-state={state}
        style={{
          top: `${headerHeight}px`,  // Position it right below the header
          height: `calc(100vh - ${headerHeight}px)` // Adjust height to account for header
        }}
      >
        <div
          data-playbook="drawer"
          className="h-full bg-background pointer-events-auto flex flex-col rounded-lg md:rounded-tl-lg"
          style={{
            maxWidth: "610px",
            height: "100%", // Use full height of parent container
            borderLeft: '0.5px solid rgba(var(--base-navy), 0.3)',
            borderTop: '0.5px solid rgba(var(--base-navy), 0.3)',
            boxShadow: '-1px 0 2px -1px rgba(var(--base-navy), 0.1)',
            transition: 'all 300ms cubic-bezier(0.25, 0.1, 0.25, 1)'
          }}
        >
          <PlaybookDrawer
            open={isPlaybookOpen}
            onClose={closePlaybook}
          />
        </div>
      </div>
    </PlaybookContext.Provider>
  );
}
