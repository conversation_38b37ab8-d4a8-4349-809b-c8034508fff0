import { cn } from "@/lib/utils";
import { LogoIqidis } from "@/components/icons";
import { useTheme } from "next-themes";

interface PageLoaderProps {
  message?: string;
}

export function PageLoader({ message = "Loading..." }: PageLoaderProps) {
  const { resolvedTheme } = useTheme();
  
  return (
    <div className="fixed inset-0 bg-white dark:bg-gray-950 bg-opacity-80 flex flex-col items-center justify-center z-[999]">
      <div className="animate-spin-slow">
        <div className="relative flex items-center justify-center">
          <LogoIqidis
            size={100}
            mixBlendMode={resolvedTheme === "dark" ? "lighten" : "multiply"}
            isDark={resolvedTheme === "dark"}
          />
        </div>
      </div>
      <p className={cn("text-lg font-medium mt-4")}>{message}</p>
    </div>
  );
}




