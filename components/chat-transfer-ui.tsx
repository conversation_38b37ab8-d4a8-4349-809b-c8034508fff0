"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { ArrowLeftIcon, PlusIcon, FileIcon } from "lucide-react";
import { LoaderIcon } from "@/components/icons";
import {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { logEvent } from "@/lib/analytics/events-client";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";
import { useUser } from "@/contexts/UserContext";

export function ChatTransferUI({
  chatId,
  userId,
}: {
  chatId: string;
  userId: string;
}) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [summary, setSummary] = useState("");
  const [attachments, setAttachments] = useState<
    Array<{
      id: string;
      name: string;
      url: string;
      selected: boolean;
    }>
  >([]);
  const [originalChat, setOriginalChat] = useState<any>(null);
  const user = useUser();

  useEffect(() => {
    async function fetchChatData() {
      try {
        setLoading(true);
        const response = await fetch(`/api/chat/transfer?chatId=${chatId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch chat data");
        }

        const data = await response.json();
        setOriginalChat(data.chat);

        // Set the backend-generated summary
        setSummary(data.summary || "");

        // Initialize attachments with selected=true
        setAttachments(
          (data.attachments || []).map((attachment: any) => ({
            id: attachment.id,
            name: attachment.name,
            url: attachment.url,
            selected: true,
          }))
        );

        setLoading(false);
      } catch (error) {
        toast.error("Failed to load chat data");
        console.error(error);
        setLoading(false);
      }
    }

    fetchChatData();

    // Log the event when chat transfer page is loaded
    logEvent(SiteBehaviorEvent.TRANSFER_CHAT, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      chatId,
      step: "viewed transfer page",
      stepNumber: "2",
    });
  }, [chatId]);

  async function handleCreateNewChat() {
    try {
      setCreating(true);
      // Log the event when chat transfer page is loaded
      logEvent(SiteBehaviorEvent.TRANSFER_CHAT, {
        userId: user?.id,
        userEmail: user?.email,
        isAdmin: user?.isAdmin,
        chatId,
        step: "clicked create new chat",
        stepNumber: "3",
      });

      const selectedAttachments = attachments.filter(
        (attachment) => attachment.selected
      );

      const response = await fetch("/api/chat/transfer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          summary,
          originalChatId: chatId,
          selectedAttachments,
          modelId: originalChat?.modelId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle specific chat limit exceeded error
        if (errorData.error === "CHAT_LIMIT_EXCEEDED") {
          toast.error(
            "You've reached your chat limit. Please delete some chats before creating a new one."
          );
          setCreating(false);
          return;
        }

        throw new Error(errorData.error || "Failed to create new chat");
      }

      const { chatId: newChatId } = await response.json();

      toast.success("New chat created successfully");
      router.push(`/chat/${newChatId}`);
    } catch (error) {
      toast.error("Failed to create new chat");
      console.error(error);
      setCreating(false);
    }
  }

  if (loading) {
    return (
      <div className="flex h-[calc(100vh-theme(spacing.16))] flex-col items-center justify-center">
        <div className="animate-spin">
          <LoaderIcon size={32} />
        </div>
        <p className="text-sm text-muted-foreground mt-4">
          Loading chat data and generating summary...
        </p>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-6 px-4 md:px-6 md:py-8">
      <div className="flex items-center gap-3 mb-6">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/chat/${chatId}`)}
              className="h-8 w-8"
            >
              <ArrowLeftIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Return to chat</TooltipContent>
        </Tooltip>

        <h1 className="text-xl md:text-2xl font-semibold">
          Transfer Chat to New Conversation
        </h1>
      </div>

      <Card className="mb-6 border shadow-sm">
        <CardHeader className="pb-3">
          <h2 className="text-lg font-medium">Edit Summary</h2>
          <p className="text-sm text-muted-foreground">
            This summary will be the first message in your new chat.
          </p>
        </CardHeader>
        <CardContent>
          <Textarea
            value={summary}
            onChange={(e) => setSummary(e.target.value)}
            className="min-h-[240px] resize-y font-medium text-sm"
            placeholder="Edit the summary to include the most relevant information..."
          />
        </CardContent>
      </Card>
      {attachments.length > 0 && (
        <Card className="mb-6 border shadow-sm">
          <CardHeader className="pb-3">
            <h2 className="text-lg font-medium">Select Attachments</h2>
            <p className="text-sm text-muted-foreground">
              Choose which files to include in the new chat.
            </p>
          </CardHeader>
          <CardContent>
            <div className="max-h-[300px] overflow-y-auto pr-1">
              <div className="space-y-2">
                {attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="flex items-center space-x-3 p-3 rounded-md border bg-background/50 hover:bg-accent/50 transition-colors"
                  >
                    <Checkbox
                      id={attachment.id}
                      checked={attachment.selected}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setAttachments(
                          attachments.map((a) =>
                            a.id === attachment.id
                              ? { ...a, selected: !!e.target.checked }
                              : a
                          )
                        );
                      }}
                      className="h-5 w-5"
                    />
                    <label
                      htmlFor={attachment.id}
                      className="flex items-center flex-1 cursor-pointer text-sm font-medium"
                    >
                      <FileIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="truncate">{attachment.name}</span>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-2 flex justify-between border-t">
            <div className="text-sm text-muted-foreground">
              {attachments.filter((a) => a.selected).length} of{" "}
              {attachments.length} selected
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  setAttachments(
                    attachments.map((a) => ({ ...a, selected: true }))
                  )
                }
              >
                Select all
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  setAttachments(
                    attachments.map((a) => ({ ...a, selected: false }))
                  )
                }
              >
                Deselect all
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}

      <div className="flex justify-end space-x-3 mt-8">
        <Button
          variant="outline"
          onClick={() => router.push(`/chat/${chatId}`)}
        >
          Cancel
        </Button>
        <Button
          onClick={handleCreateNewChat}
          disabled={creating || !summary.trim()}
          className="gap-2 navy-button"
        >
          {creating ? (
            <span className="animate-spin absolute right-4">
              <LoaderIcon />
            </span>
          ) : (
            <PlusIcon className="h-4 w-4" />
          )}
          Create New Chat
        </Button>
      </div>
    </div>
  );
}
