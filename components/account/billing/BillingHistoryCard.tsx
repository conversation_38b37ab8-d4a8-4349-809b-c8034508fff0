
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle, CardDescription, CardFooter } from "@/components/ui/card";
import { FileText, ExternalLink } from "lucide-react";
import Link from 'next/link';
import { useEffect, useState } from "react";
import { format, startOfYear } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { useSession } from "next-auth/react";

interface Invoice {
  id: string;
  number: string;
  created: number;
  amount_paid: number;
  currency: string;
  status: string;
  invoice_pdf: string | null;
  hosted_invoice_url: string | null;
  period_start: number;
  period_end: number;
}

interface BillingHistoryCardProps {
  tier?: "free" | "premium" | "premium-yearly" | "enterprise";
}

export function BillingHistoryCard({ tier = "free" }: BillingHistoryCardProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status: authStatus } = useSession();
  
  // Get isAdminManaged directly from session
  const isAdminManaged = session?.user?.isAdminManaged === true;

  // Add a console log to debug
  useEffect(() => {
    console.log("isAdminManaged:", isAdminManaged);
  }, [isAdminManaged]);

  // Reset state when auth status changes
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      setInvoices([]);
      setError(null);
    }
  }, [authStatus]);

  // Only fetch invoices when authenticated and on a paid tier
  useEffect(() => {
    if (authStatus === "authenticated" && tier && tier !== "free") {
      fetchInvoices();
    }
  }, [tier, authStatus]);

  const fetchInvoices = async () => {
    // Don't attempt to fetch if not authenticated
    if (authStatus !== "authenticated") {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/stripe/invoices');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.error || `Failed to fetch invoices: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (Array.isArray(data.invoices)) {
        setInvoices(data.invoices);
      } else {
        console.error("Invalid invoice data format:", data);
        setInvoices([]);
        setError("Invalid invoice data received");
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      setError(error instanceof Error ? error.message : "Failed to load invoices");
      setInvoices([]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    if (typeof amount !== 'number' || !currency) {
      return '$0.00';
    }
    
    try {
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency.toUpperCase(),
        minimumFractionDigits: 2
      });
      
      return formatter.format(amount / 100); // Stripe amounts are in cents
    } catch (e) {
      console.error("Error formatting currency:", e);
      return `${amount/100} ${currency}`;
    }
  };

  const formatDate = (timestamp: number) => {
    if (!timestamp) return 'Unknown date';
    
    try {
      return format(new Date(timestamp * 1000), "MMM d, yyyy");
    } catch (e) {
      console.error("Error formatting date:", e);
      return 'Invalid date';
    }
  };

  // Filter invoices for the current year
  const currentYearStart = startOfYear(new Date()).getTime();
  const currentYearInvoices = invoices.filter(invoice => 
    invoice && invoice.created && invoice.created * 1000 >= currentYearStart
  );
  const hasOlderInvoices = invoices.length > currentYearInvoices.length;

  const handleViewFullHistory = async () => {
    // Don't attempt to open portal if not authenticated
    if (authStatus !== "authenticated") {
      return;
    }
    
    try {
      setIsLoading(true);
      const response = await fetch('/api/stripe/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to create portal session');
      }
      
      const { url } = await response.json();
      if (url) {
        window.location.href = url;
      } else {
        throw new Error('No URL returned from portal session');
      }
    } catch (error) {
      console.error('Error opening customer portal:', error);
      setError(error instanceof Error ? error.message : "Failed to open billing portal");
    } finally {
      setIsLoading(false);
    }
  };

  // Determine if we're on a paid tier and authenticated
  const isPaidTier = tier && tier !== "free";
  const isAuthenticated = authStatus === "authenticated";
  const isAuthLoading = authStatus === "loading";

  // Don't render anything while auth is loading
  if (isAuthLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>View and download your invoices</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Billing History</CardTitle>
            <CardDescription>View and download your invoices for {new Date().getFullYear()}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {!isAuthenticated ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">Please log in to view your billing history</p>
          </div>
        ) : !isPaidTier ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">No billing history available on the Free plan</p>
            <Button variant="outline" className="mt-4" asChild>
              <Link href="/subscription">
                <span className="hidden sm:block">Upgrade to Access Billing History</span>
                <span className="block sm:hidden">View Billing History</span>
              </Link>
            </Button>
          </div>
        ) : isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        ) : error ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">Error loading invoices: {error}</p>
            <Button variant="outline" className="mt-4" onClick={fetchInvoices}>
              Retry
            </Button>
          </div>
        ) : invoices.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">No invoices found</p>
          </div>
        ) : currentYearInvoices.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">No invoices found for {new Date().getFullYear()}</p>
            {hasOlderInvoices && (
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={handleViewFullHistory}
              >
                View Previous Years
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {currentYearInvoices.map((invoice) => (
              <div key={invoice.id} className="flex justify-between items-center p-3 border rounded-md bg-muted/40">
                <div className="flex items-center space-x-4">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Invoice #{invoice.number || 'Unknown'}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(invoice.created)} • {formatCurrency(invoice.amount_paid, invoice.currency)}
                    </p>
                  </div>
                </div>
                {invoice.invoice_pdf && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => window.open(invoice.invoice_pdf!, '_blank')}
                  >
                    Download
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {isAuthenticated && isPaidTier && !isLoading && !error && invoices.length > 0 && !isAdminManaged && (
        <CardFooter className="border-t pt-4">
          <div className="w-full flex justify-center">
            <Button 
              variant="link" 
              className="text-primary flex items-center"
              onClick={handleViewFullHistory}
              disabled={isLoading}
            >
              {hasOlderInvoices ? "View Complete Billing History" : "Manage Billing in Stripe"}
              <ExternalLink className="h-3 w-3 ml-1" />
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
