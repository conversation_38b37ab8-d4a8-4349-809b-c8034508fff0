import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { List, Loader2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, ArrowUpDown } from "lucide-react";
import { format } from "date-fns";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect } from "react";

interface ReferralData {
  id?: string;
  name: string;
  email: string;
  date: string;
  status: "pending" | "converted" | "expired";
  reward: string;
}

interface ReferralTableCardProps {
  referrals: ReferralData[];
  isLoading: boolean;
}

type SortField = "name" | "email" | "date" | "status" | "reward";
type SortDirection = "asc" | "desc";

export default function ReferralTableCard({
  referrals,
  isLoading,
}: ReferralTableCardProps) {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Sorting state
  const [sortField, setSortField] = useState<SortField>("date");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  
  // Reset to first page when referrals data changes
  useEffect(() => {
    setCurrentPage(1);
  }, [referrals]);
  
  // Handle sort click
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default direction
      setSortField(field);
      // Default to ascending for most fields, but descending for date
      setSortDirection(field === "date" ? "desc" : "asc");
    }
  };
  
  // Sort referrals based on current sort field and direction
  const sortedReferrals = [...(referrals || [])].sort((a, b) => {
    const direction = sortDirection === "asc" ? 1 : -1;
    
    switch (sortField) {
      case "name":
        return direction * a.name.localeCompare(b.name);
      case "email":
        return direction * a.email.localeCompare(b.email);
      case "date":
        return direction * (new Date(a.date).getTime() - new Date(b.date).getTime());
      case "status":
        return direction * a.status.localeCompare(b.status);
      case "reward":
        return direction * a.reward.localeCompare(b.reward);
      default:
        return 0;
    }
  });
  
  // Calculate pagination values
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentReferrals = sortedReferrals.slice(indexOfFirstItem, indexOfLastItem) || [];
  const totalPages = Math.ceil(sortedReferrals.length / itemsPerPage);

  // Status badge helper function
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "converted":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            Converted
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Pending
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">
            Expired
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">
            {status}
          </Badge>
        );
    }
  };

  // Helper function to format names
  const toTitleCase = (str: string) => {
    return str
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };
  
  // Helper function to render sort indicator
  const renderSortIndicator = (field: SortField) => {
    if (sortField === field) {
      return (
        <span className="ml-1 inline-block">
          {sortDirection === "asc" ? "↑" : "↓"}
        </span>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <List className="h-5 w-5 text-primary" />
          Referral History
        </CardTitle>
        <CardDescription>Track the status of your referrals</CardDescription>
      </CardHeader>
      <CardContent>
        <div
          className={`rounded-md border ${
            isLoading ? "overflow-hidden" : ""
          }`}
        >
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center">
                    Name {renderSortIndicator("name")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("email")}
                >
                  <div className="flex items-center">
                    Email {renderSortIndicator("email")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("date")}
                >
                  <div className="flex items-center">
                    Date {renderSortIndicator("date")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("status")}
                >
                  <div className="flex items-center">
                    Status {renderSortIndicator("status")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("reward")}
                >
                  <div className="flex items-center">
                    Reward {renderSortIndicator("reward")}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentReferrals.length > 0 ? (
                currentReferrals.map((referral) => (
                  <TableRow key={referral.id}>
                    <TableCell className="font-medium">
                      {toTitleCase(referral.name)}
                    </TableCell>
                    <TableCell>{referral.email}</TableCell>
                    <TableCell>
                      {format(new Date(referral.date), "MMM d, yyyy")}
                    </TableCell>
                    <TableCell>{getStatusBadge(referral.status)}</TableCell>
                    <TableCell>{referral.reward}</TableCell>
                  </TableRow>
                ))
              ) : isLoading ? (
                <TableRow>
                  <TableCell colSpan={5} className="py-6 text-center">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-10 w-10 animate-spin" />
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-6 text-muted-foreground"
                  >
                    No referrals yet
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination Controls */}
        {sortedReferrals.length > 0 && (
        <div className="flex flex-col sm:flex-row items-center justify-between mt-4 gap-4">
  <div className="text-sm text-muted-foreground">
    Showing {indexOfFirstItem + 1}-
    {Math.min(indexOfLastItem, sortedReferrals.length)} of{" "}
    {sortedReferrals.length} referrals
  </div>

  <div className="flex items-center gap-4">
    {/* Pagination Controls */}
    <div className="flex gap-1">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setCurrentPage(1)}
        disabled={currentPage === 1}
        className="hidden xs:inline-flex"  // Hide on very small screens only
      >
        <ChevronsLeft className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setCurrentPage(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      <div className="flex items-center px-2">
        <span className="text-sm whitespace-nowrap">
          {window.innerWidth < 400 ? `${currentPage}/${totalPages || 1}` : `Page ${currentPage} of ${totalPages || 1}`}
        </span>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setCurrentPage(currentPage + 1)}
        disabled={currentPage === totalPages || totalPages === 0}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setCurrentPage(totalPages)}
        disabled={currentPage === totalPages || totalPages === 0}
        className="hidden xs:inline-flex"  // Hide on very small screens only
      >
        <ChevronsRight className="h-4 w-4" />
      </Button>
    </div>

    {/* Items per page selector - moved next to pagination on mobile */}
    <div className="flex items-center gap-2">
      <span className="text-sm hidden sm:inline">Items per page:</span>
      <select
        className="border rounded p-1 text-sm"
        value={itemsPerPage}
        onChange={(e) => {
          setItemsPerPage(Number(e.target.value));
          setCurrentPage(1);
        }}
      >
        <option value={5}>5</option>
        <option value={10}>10</option>
        <option value={25}>25</option>
        <option value={50}>50</option>
      </select>
    </div>
  </div>
</div>
        )}
      </CardContent>
    </Card>
  );
}
