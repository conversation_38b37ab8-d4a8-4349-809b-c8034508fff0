import {
  <PERSON>,
  CardContent,
  <PERSON>Header,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Flame, Award, Check } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function PowerReferralCard() {
  return (
    <Card className="flex flex-col h-full hover:-translate-y-1 hover:shadow-md transition-all duration-300">
      <CardHeader className="pb-3 bg-primary/5 rounded-t-lg">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-2xl font-playfair flex items-center gap-2">
              <div className="p-1.5 bg-secondary rounded-md">
                <Flame className="h-5 w-5 text-primary" />
              </div>
              <span>Power Referrer</span>
            </CardTitle>
            <CardDescription className="mt-1">
              For high-volume referrers
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 flex-1 flex flex-col">
        <div className="text-3xl font-playfair font-bold flex items-center gap-2">
          $150{" "}
          <span className="text-sm font-normal text-muted-foreground">
            per referral
          </span>
        </div>

        <div className="space-y-3 flex-1">
          <p className="text-sm font-medium">
            Refer 5+ paid users in 60 days → Unlock $50/month per referral, for
            3 months
          </p>
          <ul className="text-sm text-muted-foreground space-y-2 pt-2">
            {[
              "That's up to $150 per referred user paid out in cash",
              "Cash is paid monthly as long as the referral stays active and paid",
              "Applies to every referral after your fourth",
            ].map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="rounded-full p-1 mt-0.5 bg-green-100">
                  <Check className="h-3 w-3 text-green-600" />
                </div>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="mt-auto pt-4">
          <Button variant="outline" className="w-full">
            {/* <Award className="h-4 w-4 mr-2" /> */}
            {/* Become a Power Referrer */}
            Coming Soon
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
