import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose, DialogFooter } from './ui/dialog';
import { X, FolderPlus, ChevronDown } from 'lucide-react';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { toast } from 'sonner';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from './ui/popover';
import { cn } from '@/lib/utils';

interface Folder {
  id: string;
  name: string;
  type: 'user' | 'system';
}

interface SavePromptDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (promptName: string, folderId: string) => void;
  defaultPromptName?: string;
}

export function SavePromptDialog({
  isOpen,
  onOpenChange,
  onSave,
  defaultPromptName = ''
}: SavePromptDialogProps) {
  const [folders, setFolders] = useState<Folder[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);
  const [selectedFolderId, setSelectedFolderId] = useState<string>("");
  const [promptName, setPromptName] = useState<string>(defaultPromptName);
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);

  // Load folders when the dialog is opened
  useEffect(() => {
    if (isOpen) {
      loadFolders();
    }
  }, [isOpen]);

  // Update prompt name when default changes
  useEffect(() => {
    if (defaultPromptName && isOpen) {
      setPromptName(defaultPromptName);
    }
  }, [defaultPromptName, isOpen]);

  // Load user folders from the API
  const loadFolders = async () => {
    try {
      setIsLoadingFolders(true);
      const response = await fetch('/api/prompts/folders?type=user');
      if (!response.ok) {
        throw new Error('Failed to load folders');
      }
      const data = await response.json();

      // Sort folders alphabetically for better UX
      const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));
      setFolders(sortedData);

      // Set default folder if available
      const defaultFolder = sortedData.find((folder: Folder) => folder.name.toLowerCase() === 'general');
      if (defaultFolder) {
        setSelectedFolderId(defaultFolder.id);
      } else if (sortedData.length > 0) {
        setSelectedFolderId(sortedData[0].id);
      }

      // Reset the new folder input state
      setShowNewFolderInput(false);
      setNewFolderName('');
    } catch (error) {
      console.error('Error loading folders:', error);
      toast.error('Failed to load folders');
    } finally {
      setIsLoadingFolders(false);
    }
  };

  // Create a new folder
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error('Please enter a folder name');
      return;
    }

    // Check if folder with same name already exists
    const folderExists = folders.some(
      folder => folder.name.toLowerCase() === newFolderName.trim().toLowerCase()
    );

    if (folderExists) {
      toast.error('A folder with this name already exists. Please choose a different name');
      return;
    }

    try {
      setIsCreatingFolder(true);
      const response = await fetch('/api/prompts/folders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newFolderName.trim() }),
      });

      if (!response.ok) {
        if (response.status === 409) {
          const errorMessage = await response.text();
          toast.error(errorMessage);
          return;
        }
        throw new Error('Failed to create folder');
      }

      const newFolder = await response.json();

      // Update folders list and select the new folder
      setFolders([...folders, newFolder]);
      setSelectedFolderId(newFolder.id);
      setNewFolderName('');
      setShowNewFolderInput(false);
      toast.success('Folder created successfully');
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error('Failed to create folder');
    } finally {
      setIsCreatingFolder(false);
    }
  };

  // Handle save
  const handleSave = () => {
    if (!promptName.trim()) {
      toast.error('Please enter a name for your prompt');
      return;
    }

    if (!selectedFolderId && !showNewFolderInput) {
      toast.error('Please select a folder');
      return;
    }

    // Close the dialog immediately for better UX
    onOpenChange(false);

    // Call the save function
    onSave(promptName, selectedFolderId);

    // Trigger a custom event to notify the Playbook component to refresh its data
    if (typeof window !== 'undefined') {
      const refreshEvent = new CustomEvent('refresh-playbook-data');
      window.dispatchEvent(refreshEvent);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[425px] z-[100]">
        <DialogHeader>
          <DialogTitle>Save to My Playbook</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Save this prompt to your playbook for future use.
          </p>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="promptName">Prompt Name</Label>
            <Input
              id="promptName"
              value={promptName}
              onChange={(e) => setPromptName(e.target.value)}
              placeholder="Enter a name for this prompt"
              autoFocus
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="folderSelect">Save to Folder</Label>
            {showNewFolderInput ? (
              <div className="flex gap-2">
                <Input
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="New folder name"
                  className="flex-1"
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && newFolderName.trim()) {
                      handleCreateFolder();
                    }
                  }}
                />
                <Button
                  size="sm"
                  onClick={handleCreateFolder}
                  disabled={isCreatingFolder || !newFolderName.trim()}
                >
                  {isCreatingFolder ? (
                    <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  ) : (
                    'Create'
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="px-2"
                  onClick={() => setShowNewFolderInput(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                <div className="flex gap-2">
                  <select
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-[rgb(var(--base-navy))] focus:border-[rgb(var(--base-navy))] disabled:cursor-not-allowed disabled:opacity-50 transition-colors"
                    value={selectedFolderId}
                    onChange={(e) => setSelectedFolderId(e.target.value)}
                    disabled={isLoadingFolders}
                  >
                    <option value="" disabled>
                      {isLoadingFolders ? "Loading folders..." : "Select folder"}
                    </option>
                    {folders.map((folder) => (
                      <option key={folder.id} value={folder.id}>
                        {folder.name}
                      </option>
                    ))}
                  </select>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowNewFolderInput(true)}
                    title="Create New Folder"
                    className="h-10 w-10"
                  >
                    <FolderPlus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!promptName.trim() || (!selectedFolderId && !showNewFolderInput)}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
