"use client";

import { useEffect, useRef } from 'react';
import { Logger } from '@/lib/utils/Logger';

interface PerformanceMonitorProps {
  children: React.ReactNode;
  name?: string;
  threshold?: number; // milliseconds
}

export function PerformanceMonitor({ 
  children, 
  name = 'Component', 
  threshold = 100 
}: PerformanceMonitorProps) {
  const startTimeRef = useRef<number>();
  const mountTimeRef = useRef<number>();

  useEffect(() => {
    // Component mounted
    mountTimeRef.current = performance.now();
    
    return () => {
      // Component unmounted
      if (mountTimeRef.current) {
        const mountDuration = performance.now() - mountTimeRef.current;
        if (mountDuration > threshold) {
          Logger.warn(`${name} was mounted for ${mountDuration.toFixed(2)}ms (threshold: ${threshold}ms)`);
        }
      }
    };
  }, [name, threshold]);

  useEffect(() => {
    // Render started
    startTimeRef.current = performance.now();
  });

  useEffect(() => {
    // Ren<PERSON> completed
    if (startTimeRef.current) {
      const renderDuration = performance.now() - startTimeRef.current;
      if (renderDuration > threshold) {
        Logger.warn(`${name} render took ${renderDuration.toFixed(2)}ms (threshold: ${threshold}ms)`);
      }
    }
  });

  return <>{children}</>;
}

// Hook for monitoring specific operations
export function usePerformanceMonitor(name: string, threshold: number = 100) {
  const startOperation = () => {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      if (duration > threshold) {
        Logger.warn(`${name} operation took ${duration.toFixed(2)}ms (threshold: ${threshold}ms)`);
      }
      return duration;
    };
  };

  return { startOperation };
}

// Debounced performance logger to prevent spam
let performanceLogQueue: Array<{ name: string; duration: number; timestamp: number }> = [];
let performanceLogTimer: NodeJS.Timeout | null = null;

export function logPerformance(name: string, duration: number, threshold: number = 100) {
  if (duration <= threshold) return;

  performanceLogQueue.push({
    name,
    duration,
    timestamp: Date.now()
  });

  if (performanceLogTimer) {
    clearTimeout(performanceLogTimer);
  }

  performanceLogTimer = setTimeout(() => {
    if (performanceLogQueue.length > 0) {
      Logger.warn('Performance issues detected:', {
        issues: performanceLogQueue.slice(),
        count: performanceLogQueue.length
      });
      performanceLogQueue = [];
    }
    performanceLogTimer = null;
  }, 1000); // Batch logs every second
}
