import { useState, useEffect } from 'react';
import { ChatWithTags as Chat, Tag } from '@/lib/db/schema';
import { extractAllUniqueTags } from '@/hooks/use-tag-management';
import { XIcon, PlusIcon, CheckIcon } from 'lucide-react';
import { PopoverContent } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DialogContent, DialogFooter, DialogHeader, DialogTitle } from './ui/dialog';
import { createPortal } from 'react-dom';
import { 
  Popover, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { TAG_COLORS, DEFAULT_TAG_COLOR, AVAILABLE_TAG_COLORS } from '@/lib/constants/tag-colors';

interface TagManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  chat: Chat | null;
  allChats: Chat[];
  onSave: (tags: Tag[]) => void;
  isLoading?: boolean;
  useDialog?: boolean; // Whether to use Dialog instead of Popover
  lightweightDialog?: boolean; // New prop for lightweight dialog without overlay
  anchorElement?: HTMLElement | null; // Element to position relative to
}

export function TagManagementDialog({
  open,
  onOpenChange,
  chat,
  allChats,
  onSave,
  isLoading = false,
  useDialog = false,
  lightweightDialog = false,
  anchorElement = null
}: TagManagementDialogProps) {
  const [tagInput, setTagInput] = useState('');
  const [chatTags, setChatTags] = useState<Tag[]>([]);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [isMounted, setIsMounted] = useState(false);
  const [colorPickerOpen, setColorPickerOpen] = useState<string | null>(null);
  const [selectedColor, setSelectedColor] = useState<string>(DEFAULT_TAG_COLOR);
  const [tagsChanged, setTagsChanged] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
    return () => {
      // Ensure cleanup when component unmounts
      setIsMounted(false);
    };
  }, []);
  
  // Calculate position based on anchor element
  useEffect(() => {
    if (lightweightDialog && anchorElement && open) {
      const rect = anchorElement.getBoundingClientRect();
      
      // Check if this is likely from a dropdown menu
      const isFromDropdown = anchorElement.closest('[role="menuitem"]') !== null;
      
      if (isFromDropdown) {
        // Get the dialog height (approximate)
        const dialogHeight = 400; // Approximate height of the dialog
        
        // Position to the right of the element for dropdown menu items
        // and center it vertically relative to the button
        setPosition({
          // Center the dialog vertically with the button
          top: rect.top + window.scrollY - (dialogHeight / 2) + (rect.height / 2),
          left: rect.right + window.scrollX + 5, // 5px gap
        });
      } else {
        // Default positioning (below the element) for other cases like chat header
        setPosition({
          top: rect.bottom + window.scrollY + 5,
          left: rect.right + window.scrollX - 350, // Align right edge
        });
      }
    }
  }, [lightweightDialog, anchorElement, open]);

  // Handle clicks outside
  useEffect(() => {
    if (!lightweightDialog || !open) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const dialogElement = document.querySelector('[data-lightweight-dialog="true"]');
      const popoverElements = document.querySelectorAll('[role="dialog"]');
      
      // Check if the click is inside any popover (color picker)
      const isInsidePopover = Array.from(popoverElements).some(el => el.contains(target));
      
      // Only close if click is outside both the dialog and any popovers
      if (dialogElement && !dialogElement.contains(target) && !isInsidePopover) {
        onOpenChange(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [lightweightDialog, open, onOpenChange]);

  // Reset state when chat changes
  useEffect(() => {
    if (chat) {
      setChatTags(chat.tags || []);
      setTagsChanged(false); // Reset change tracking when chat changes
    } else {
      setChatTags([]);
      setTagsChanged(false);
    }
  }, [chat]);

  // Track changes to tags
  useEffect(() => {
    if (!chat) return;
    
    // Check if tags have changed
    const originalTags = chat.tags || [];
    
    // Different number of tags means they changed
    if (originalTags.length !== chatTags.length) {
      setTagsChanged(true);
      return;
    }
    
    // Check if any tags are different
    const tagsAreDifferent = chatTags.some(currentTag => {
      // Find matching tag in original tags
      const originalTag = originalTags.find(t => t.name === currentTag.name);
      
      // If tag doesn't exist in original or color is different, it's changed
      return !originalTag || originalTag.color !== currentTag.color;
    });
    
    // Check if any original tags are missing from current tags
    const anyTagsMissing = originalTags.some(originalTag => 
      !chatTags.some(t => t.name === originalTag.name)
    );
    
    setTagsChanged(tagsAreDifferent || anyTagsMissing);
  }, [chat, chatTags]);

  // Get all unique existing tags from all chats
  const allExistingTags = extractAllUniqueTags(allChats);
  
  // Filtered suggestions based on input
  const tagSuggestions = allExistingTags
    .filter(tag => 
      tag.name.toLowerCase().includes(tagInput.toLowerCase()) && 
      !chatTags.some(t => t.name === tag.name)
    );
  
  const handleAddTag = (tag: string | Tag) => {
    if (typeof tag === 'string') {
      if (tag.trim() && !chatTags.some(t => t.name === tag)) {
        setChatTags([...chatTags, { name: tag, color: selectedColor }]);
        setTagInput('');
      }
    } else {
      if (tag.name.trim() && !chatTags.some(t => t.name === tag.name)) {
        setChatTags([...chatTags, tag]);
        setTagInput('');
      }
    }
  };
  
  const handleRemoveTag = (tagToRemove: string) => {
    setChatTags(chatTags.filter(tag => tag.name !== tagToRemove));
  };
  
  const handleUpdateTagColor = (tagName: string, color: string) => {
    // Update the tag color in the local state
    setChatTags(chatTags.map(tag => 
      tag.name === tagName ? { ...tag, color } : tag
    ));
    // Close only the color picker popover, not the main dialog
    setColorPickerOpen(null);
  };
  
  const handleSaveTags = () => {
    // Close the dialog first to prevent duplicate renders
    onOpenChange(false);
    // Then save the tags
    onSave(chatTags);
  };

  if (!chat) return null;

  // The content is the same, just the container changes
  const content = (
    <>
      <div className="p-4 border-b bg-white dark:bg-gray-900">
        <h3 className="font-medium">Manage Tags</h3>
      </div>
      
      <div className="space-y-4 p-4 bg-white dark:bg-gray-900">
        <div className="flex flex-col space-y-2">
          <label htmlFor="tag-input" className="text-sm font-medium">
            Tag Name
          </label>
          <div className="flex items-center space-x-2">
            <Input
              id="tag-input"
              placeholder="Enter tag name"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && tagInput.trim()) {
                  e.preventDefault();
                  handleAddTag(tagInput.trim());
                }
              }}
              className="flex-1 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800"
              disabled={isLoading}
            />
            <Button 
              onClick={() => handleAddTag(tagInput.trim())}
              disabled={!tagInput.trim() || isLoading}
              size="sm"
            >
              <PlusIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Color selection for new tags */}
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium">
            Tag Color
          </label>
          <div className="flex flex-wrap gap-2">
            {Object.entries(TAG_COLORS).map(([name, color]) => (
              <button
                key={name}
                type="button"
                className={`w-8 h-8 rounded-full flex items-center justify-center transition-all ${
                  selectedColor === color ? 'ring-2 ring-offset-2 ring-primary' : ''
                }`}
                style={{ backgroundColor: color }}
                onClick={() => setSelectedColor(color)}
                aria-label={`Select ${name} color`}
              >
                {selectedColor === color && (
                  <CheckIcon className="h-4 w-4 text-white" />
                )}
              </button>
            ))}
          </div>
        </div>
        
        {/* Tag suggestions */}
        {tagSuggestions.length > 0 && tagInput.trim() !== '' && (
          <div className="mt-2">
            <label className="text-sm font-medium mb-2 block">
              Suggestions
            </label>
            <div className="flex flex-wrap gap-2">
              {tagSuggestions.slice(0, 5).map((tag) => (
                <Badge 
                  key={tag.name} 
                  variant="outline" 
                  className="cursor-pointer hover:bg-accent"
                  onClick={() => handleAddTag(tag)}
                  style={{ borderColor: tag.color, color: tag.color }}
                >
                  {tag.name}
                  <PlusIcon className="h-3 w-3 ml-1" />
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {/* Current tags */}
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Current Tags</h4>
          <div className="flex flex-wrap gap-2">
            {chatTags.length > 0 ? (
              chatTags.map((tag) => (
                <div key={tag.name} className="flex items-center">
                  <Popover 
                    open={colorPickerOpen === tag.name}
                    onOpenChange={(open) => setColorPickerOpen(open ? tag.name : null)}
                  >
                    <PopoverTrigger asChild>
                      <Badge 
                        variant="secondary" 
                        className="flex items-center gap-1 cursor-pointer"
                        style={{ borderColor: tag.color, backgroundColor: `${tag.color}20` }}
                      >
                        <div 
                          className="w-2 h-2 rounded-full mr-1" 
                          style={{ backgroundColor: tag.color }}
                        />
                        {tag.name}
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveTag(tag.name);
                          }}
                          className="ml-1 rounded-full hover:bg-accent p-0.5"
                          disabled={isLoading}
                        >
                          <XIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-2">
                      <div className="grid grid-cols-3 gap-1">
                        {Object.entries(TAG_COLORS).map(([name, color]) => (
                          <button
                            key={name}
                            className="w-6 h-6 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: color }}
                            onClick={(e) => {
                              // Stop propagation to prevent closing the main dialog
                              e.stopPropagation();
                              handleUpdateTagColor(tag.name, color);
                            }}
                          >
                            {tag.color === color && (
                              <CheckIcon className="h-4 w-4 text-white" />
                            )}
                          </button>
                        ))}
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              ))
            ) : (
              <span className="text-sm text-muted-foreground">No tags added yet</span>
            )}
          </div>
        </div>
      </div>
      
      <div className="flex justify-end gap-2 p-4 border-t bg-white dark:bg-gray-900">
        <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
          Cancel
        </Button>
        <Button onClick={handleSaveTags} disabled={isLoading || !tagsChanged}>
          {isLoading ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </>
  );

  // Return appropriate container based on props
  if (lightweightDialog) {
    // Only render in a portal when mounted (client-side) and when open
    if (!isMounted || !open) return null;
    
    const lightweightDialogContent = (
      <div 
        className="fixed z-50 bg-background border rounded-md shadow-md w-[350px] p-0 MobileTag"
        style={{ 
          top: `${position.top}px`, 
          left: `${position.left}px`,
        }}
        data-lightweight-dialog="true"
      >
        {content}
      </div>
    );
    
    // Use createPortal to render outside the sidebar's DOM hierarchy
    return createPortal(
      lightweightDialogContent,
      document.body
    );
  } else if (useDialog) {
    return (
      <DialogContent className="max-w-[350px] p-0">
        {content}
      </DialogContent>
    );
  } else {
    return (
      <PopoverContent className="max-w-[350px] p-0" align="end" sideOffset={5}>
        {content}
      </PopoverContent>
    );
  }
}
