"use client";

import { isToday, isYesterday, subMonths, subWeeks } from "date-fns";
import Link from "next/link";
import { useParams, usePathname, useRouter } from "next/navigation";
import type { User } from "next-auth";
import { memo, useEffect, useState, useMemo, useRef } from "react";
import { toast } from "sonner";
import useSWR, { mutate } from "swr";
import { ArrowRight as ArrowRightIcon, PinIcon, TagIcon } from "lucide-react";
import { useDrag, useDrop } from "react-dnd";
import { ItemTypes } from "@/lib/dnd-types";
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Badge } from "@/components/ui/badge";
import { useTagManagement } from "@/hooks/use-tag-management";
import { TagManagementDialog } from "@/components/tag-management-dialog";

import {
  CheckCircleFillIcon,
  GlobeIcon,
  LockIcon,
  MoreH<PERSON>zontalIcon,
  ShareIcon,
  TrashIcon,
  PenIcon,
} from "@/components/icons";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { ChatWithTags as Chat } from "@/lib/db/schema";
import { fetcher } from "@/lib/utils";
import { useChatVisibility } from "@/hooks/use-chat-visibility";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SidebarOrganization } from "@/components/sidebar-organization";
import { BulkDeleteDialog } from "@/components/bulk-delete-dialog";
import { logEvent } from "@/lib/analytics/events-client";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";

type GroupedChats = {
  today: Chat[];
  yesterday: Chat[];
  lastWeek: Chat[];
  lastMonth: Chat[];
  older: Chat[];
};

interface Matter {
  id: string;
  name: string;
  chats: Chat[];
}

interface PinnedChat {
  chatId: string;
  position: number;
  chat: Chat;
}

interface SidebarData {
  chats: Chat[];
  folders: Matter[]; // We'll keep using "matters" in our component
  pinnedChats: PinnedChat[];
}

type DragItem = {
  type: string;
  id: string;
  source: "history" | "pinned" | "folder";
  folderId?: string;
  index?: number;
};

const handleRename = async (chatId: string, newTitle: string) => {
  try {
    const response = await fetch(`/api/chat`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id: chatId, title: newTitle }),
    });

    if (response.ok) {
      // Only update UI and revalidate if backend update was successful
      mutate("/api/chat-org/sidebar-data");
      return true;
    } else {
      throw new Error("Failed to rename chat");
    }
  } catch (error) {
    toast.error("Failed to rename chat.");
    return false; // Return failure status to the caller
  }
};

const PureChatItem = ({
  chat,
  isActive,
  onDelete,
  onPinChat,
  onUnpinChat, 
  setOpenMobile,
  onBulkDelete,
  pinnedChats,
  onManageTags,
}: {
  chat: Chat;
  isActive: boolean;
  onDelete: (chatId: string) => void;
  onPinChat: (chatId: string) => void;
  onUnpinChat: (chatId: string) => void;
  setOpenMobile: (open: boolean) => void;
  onBulkDelete: () => void;
  pinnedChats: PinnedChat[];
  onManageTags?: () => void;
}) => {
  const { visibilityType, setVisibilityType } = useChatVisibility({
    chatId: chat.id,
    initialVisibility: chat.visibility,
  });
  const router = useRouter();

  // Check if the current chat is pinned
  const isPinned = useMemo(() => 
    pinnedChats.some(pinnedChat => pinnedChat.chatId === chat.id),
    [chat.id, pinnedChats]
  );

  const [isEditing, setIsEditing] = useState(false);
  const [displayTitle, setDisplayTitle] = useState(chat.title);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);

  const handleSubmitRename = async (e: React.FormEvent) => {
    e.preventDefault();
    if (displayTitle && displayTitle !== chat.title) {
      const success = await handleRename(chat.id, displayTitle);
      if (!success) {
        setDisplayTitle(chat.title);
      }
    }
    setIsEditing(false);
  };

  const formatTime = (dateString: Date | string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  return (
    <SidebarMenuItem
      className={`relative ${
        isEditing
          ? "border-2 border-primary rounded-md"
          : "after:content-[''] after:absolute after:left-[5%] after:right-[5%] after:bottom-0 after:h-[0.5px] after:bg-gradient-to-r after:from-transparent after:via-[rgb(var(--base-navy))] after:to-transparent after:opacity-10 after:blur-[0.5px] last:after:hidden"
      }`}
    >
      <SidebarMenuButton
        asChild
        isActive={isActive}
        className="h-auto pt-1.5 pb-1 pl-1 flex items-start w-full"
      >
        {isEditing ? (
          <form onSubmit={handleSubmitRename} className="flex-1">
            <input
              type="text"
              value={displayTitle}
              onChange={(e) => setDisplayTitle(e.target.value)}
              onBlur={handleSubmitRename}
              autoFocus
              className="w-full bg-transparent outline-none pl-1"
            />
          </form>
        ) : (
          <div className="flex w-full pr-8">
            <Link
              href={`/chat/${chat.id}`}
              onClick={() => setOpenMobile(false)}
              className="min-w-0 flex-1 px-2 pb-1.5"
            >
              <div className="flex flex-col gap-0.5">
                <div className="flex items-center gap-1">
                  {isPinned && (
                    <PinIcon className="h-3 w-3 flex-shrink-0 text-primary dark:text-white" />
                  )}
                  <span className="truncate text-sm block font-semibold text-black dark:text-white">
                    {displayTitle}
                  </span>
                </div>
                <span className="text-[11px] text-sidebar-foreground/40 block truncate leading-tight">
                  {formatTime(chat.updatedAt || chat.createdAt)}
                </span>
                
                {/* Display tags with colors if present */}
                {chat.tags && chat.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {chat.tags.map((tag, i) => (
                      <Badge 
                        key={i} 
                        className="px-1.5 py-0 text-[10px] h-4 rounded-lg truncate max-w-[80px] border-0 font-normal"
                        style={{ 
                          backgroundColor: `${tag.color}20`, 
                          color: tag.color,
                          borderColor: tag.color
                        }}
                      >
                        <span className="truncate">{tag.name}</span>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </Link>
          </div>
        )}
      </SidebarMenuButton>

      <DropdownMenu modal={true}>
        <DropdownMenuTrigger asChild>
          <SidebarMenuAction
            className="absolute right-2 top-2.5 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            showOnHover={!isActive}
          >
            <MoreHorizontalIcon />
            <span className="sr-only">More</span>
          </SidebarMenuAction>
        </DropdownMenuTrigger>

        <DropdownMenuContent side="bottom" align="end">
          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="cursor-pointer">
              <ShareIcon />
              <span>Share</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuItem
                  className="cursor-pointer flex-row justify-between"
                  onClick={() => {
                    setVisibilityType("private");
                  }}
                >
                  <div className="flex flex-row gap-2 items-center">
                    <LockIcon size={12} />
                    <span>Private</span>
                  </div>
                  {visibilityType === "private" ? (
                    <CheckCircleFillIcon />
                  ) : null}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer flex-row justify-between"
                  onClick={() => {
                    setVisibilityType("public");
                  }}
                >
                  <div className="flex flex-row gap-2 items-center">
                    <GlobeIcon />
                    <span>Shareable</span>
                  </div>
                  {visibilityType === "public" ? <CheckCircleFillIcon /> : null}
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>

          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => isPinned ? onUnpinChat(chat.id) : onPinChat(chat.id)}
          >
            <PinIcon />
            <span>{isPinned ? "Unpin" : "Pin"}</span>
          </DropdownMenuItem>

          {/* Add Manage Tags option */}
          {onManageTags && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onManageTags();
              }}
            >
              <TagIcon/>
              <span>Tags</span>
            </DropdownMenuItem>
          )}

          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => {
              setIsEditing(true);
            }}
          >
            <PenIcon />
            <span>Rename</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
            onSelect={() => onDelete(chat.id)}
          >
            <TrashIcon />
            <span>Delete</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
            onClick={() => onBulkDelete()}
          >
            <TrashIcon />
            <span>Bulk Delete</span>
          </DropdownMenuItem>

          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => {
                  router.push(`/chat/transfer/${chat.id}`);
                  setOpenMobile(false);
                }}
              >
                <ArrowRightIcon />
                <span>Transfer</span>
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left" className="max-w-[200px]">
              Transfer this chat to a new one if it is getting too long
            </TooltipContent>
          </Tooltip>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
};

export const ChatItem = memo(function ChatItem({
  chat,
  isActive,
  onDelete,
  onPinChat,
  onUnpinChat,
  setOpenMobile,
  onBulkDelete,
  pinnedChats,
  onAddToFolder,
  onManageTags,
}: {
  chat: Chat;
  isActive: boolean;
  onDelete: (chatId: string) => void;
  onPinChat: (chatId: string) => void;
  onUnpinChat: (chatId: string) => void;
  setOpenMobile: (open: boolean) => void;
  onBulkDelete: () => void;
  pinnedChats: PinnedChat[];
  onAddToFolder?: (chatId: string, folderId: string) => void;
  onManageTags?: () => void;
}) {
  const isPinned = pinnedChats.some(p => p.chatId === chat.id);
  const ref = useRef<HTMLDivElement>(null);
  
  // Set up drag
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.CHAT,
    item: { 
      type: ItemTypes.CHAT, 
      id: chat.id,
      source: isPinned ? "pinned" : "history" 
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  
  // Apply the drag ref to our element
  drag(ref);
  
  return (
    <div 
      ref={ref}
      className={`${isDragging ? 'opacity-50' : 'opacity-100'}`}
    >
      <PureChatItem
        chat={chat}
        isActive={isActive}
        onDelete={onDelete}
        onPinChat={onPinChat}
        onUnpinChat={onUnpinChat}
        setOpenMobile={setOpenMobile}
        onBulkDelete={onBulkDelete}
        pinnedChats={pinnedChats}
        onManageTags={onManageTags}
      />
    </div>
  );
});

export function SidebarHistory({
  user,
  searchQuery,
}: {
  user: User | undefined;
  searchQuery: string;
}) {
  const { setOpenMobile } = useSidebar();
  const { id } = useParams();
  const pathname = usePathname();
  const {
    data: sidebarData,
    isLoading,
    mutate,
  } = useSWR<SidebarData>(user ? "/api/chat-org/sidebar-data" : null, fetcher, {
    fallbackData: { chats: [], folders: [], pinnedChats: [] },
    revalidateOnFocus: true,
    dedupingInterval: 5000, // Only revalidate after 5 seconds
  });

  // Add tag management hook
  const { 
    isDialogOpen, 
    currentChat, 
    openTagDialog, 
    closeTagDialog, 
    handleSaveTags,
    isLoading: isTagsLoading
  } = useTagManagement({
    onUpdateTags: async (chatId, tags) => {
      // Update local state first
      mutate(
        currentData => {
          if (!currentData) return currentData;
          
          return {
            ...currentData,
            chats: currentData.chats.map(chat => 
              chat.id === chatId ? { ...chat, tags } : chat
            )
          };
        },
        false
      );
      
      // Then trigger global revalidation
      mutate();
    }
  });

  // Add this effect to revalidate when the pathname changes
  useEffect(() => {
    mutate();
  }, [pathname, mutate]);

  const filteredHistory = useMemo(() => {
    if (!sidebarData?.chats) return [];
    if (!searchQuery) return sidebarData.chats;
    return sidebarData.chats.filter((chat) =>
      chat.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [sidebarData?.chats, searchQuery]);

  // Remove the local state and useEffect that was causing the infinite loop
  // Instead, access pinnedChats and matters directly from sidebarData
  const pinnedChats = useMemo(() => 
    sidebarData?.pinnedChats?.map(item => item.chat) || [],
  [sidebarData?.pinnedChats]);
  
  const matters = useMemo(() => 
    sidebarData?.folders || [],
  [sidebarData?.folders]);

  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const router = useRouter();
  const handleDelete = async () => {
    const deletePromise = fetch(`/api/chat?id=${deleteId}`, {
      method: "DELETE",
    });

    toast.promise(deletePromise, {
      loading: "Deleting chat...",
      success: () => {
        mutate((sidebarData) => {
          if (sidebarData) {
            return {
              ...sidebarData,
              chats: sidebarData.chats.filter((h) => h.id !== id),
              pinnedChats: sidebarData.pinnedChats.filter((p) => p.chatId !== id),
            };
          }
        });

        logEvent(SiteBehaviorEvent.DELETE_CHAT, {
          chatId: deleteId
        })
        return "Chat deleted successfully";
      },
      error: "Failed to delete chat",
    });

    setShowDeleteDialog(false);

    if (deleteId === id) {
      router.push("/");
    }
  };

  const handleBulkDelete = async (chatIds: string[]) => {
    try {
      const response = await fetch("/api/chat/bulk-delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ chatIds }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete chats");
      }

      // Update the UI by removing the deleted chats
      mutate((sidebarData) => {
        if (sidebarData) {
          return {
            ...sidebarData,
            chats: sidebarData.chats.filter((h) => !chatIds.includes(h.id)),
            pinnedChats: sidebarData.pinnedChats.filter((p) => !chatIds.includes(p.chatId)),
          };
        }
      });

      // If current chat is deleted, redirect to home
      if (id && typeof id === 'string' && chatIds.includes(id)) {
        router.push("/");
      }
    } catch (error) {
      toast.error("Failed to delete chats");
      throw error;
    }
  };

  // Add these state variables and handlers to SidebarHistory
  const handlePinChat = async (chatId: string) => {
    const chatToPin = filteredHistory.find(chat => chat.id === chatId);
    if (!chatToPin) return;

    // Check if already pinned
    if (pinnedChats.some(chat => chat.id === chatId)) {
      toast.info("Chat is already pinned");
      return;
    }

    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        // Create a simple pinnedChat entry for the UI
        const newPinnedChat = {
          chatId: chatToPin.id,
          position: (currentData.pinnedChats?.length || 0) + 1,
          chat: chatToPin
        };
        
        return {
          ...currentData,
          pinnedChats: [...(currentData.pinnedChats || []), newPinnedChat]
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch("/api/chat-org/pinned-chats", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatId }),
      });
      
      if (!response.ok) throw new Error("Failed to pin chat");
      
      // Revalidate to get actual data
      mutate();
      // toast.success("Chat pinned successfully");
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to pin chat");
    }
  };

  const handleUnpinChat = async (chatId: string) => {
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        return {
          ...currentData,
          pinnedChats: currentData.pinnedChats.filter(
            pinnedChat => pinnedChat.chatId !== chatId
          )
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch(`/api/chat-org/pinned-chats?chatId=${chatId}`, {
        method: "DELETE",
      });
      
      if (!response.ok) throw new Error("Failed to unpin chat");
      
      // Revalidate to get actual data
      mutate();
      // toast.success("Chat unpinned successfully");
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to unpin chat");
    }
  };

  const handleReorderPins = async (chatIds: string[]) => {
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        // Create a new array of pinnedChats in the desired order
        // Use filter first to ensure we only work with valid entries
        const reorderedPinnedChats = chatIds
          .map(id => {
            const pinnedChat = currentData.pinnedChats.find(
              pinnedChat => pinnedChat.chatId === id
            );
            return pinnedChat ? { ...pinnedChat } : null;
          })
          .filter((chat): chat is PinnedChat => chat !== null); // Type guard to remove nulls
        
        return {
          ...currentData,
          pinnedChats: reorderedPinnedChats
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch("/api/chat-org/pinned-chats", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatIds }),
      });
      
      if (!response.ok) throw new Error("Failed to reorder pinned chats");
      
      // Revalidate to get actual data
      mutate();
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to reorder pinned chats");
    }
  };

  // Matter handlers
  const handleAddMatter = async (name: string) => {
    if (!name.trim()) return;
    
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        // Create a temporary matter with minimal required fields
        const newMatter = {
          id: `temp-${Date.now()}`,
          name,
          chats: []
        };
        
        return {
          ...currentData,
          folders: [...currentData.folders, newMatter]
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch("/api/chat-org/folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name }),
      });
      
      if (!response.ok) throw new Error("Failed to create matter");
      
      // Revalidate to get actual data
      mutate();
      // toast.success(`Matter "${name}" created successfully`);
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to create matter");
    }
  };

  const handleRenameMatter = async (matterId: string, newName: string) => {
    if (!newName.trim()) return;
    
    // Find the current matter name
    const matter = matters.find(m => m.id === matterId);
    if (!matter) return;
    
    const oldName = matter.name;
    
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        return {
          ...currentData,
          folders: currentData.folders.map(matter => 
            matter.id === matterId ? { ...matter, name: newName } : matter
          )
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch("/api/chat-org/folders", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ oldName, newName }),
      });
      
      if (!response.ok) throw new Error("Failed to rename matter");
      
      // Revalidate to get actual data
      mutate();
      // toast.success(`Matter renamed to "${newName}" successfully`);
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to rename matter");
    }
  };

  const handleDeleteMatter = async (matterId: string) => {
    // Find the matter name
    const matter = matters.find(m => m.id === matterId);
    if (!matter) return;
    
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        return {
          ...currentData,
          folders: currentData.folders.filter(matter => matter.id !== matterId)
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch(`/api/chat-org/folders?name=${encodeURIComponent(matter.name)}`, {
        method: "DELETE",
      });
      
      if (!response.ok) throw new Error("Failed to delete matter");
      
      // Revalidate to get actual data
      mutate();
      // toast.success("Matter deleted successfully");
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to delete matter");
    }
  };

  const handleAddChatToMatter = async (matterId: string, chatId: string) => {
    // Find the matter name and chat
    const matter = matters.find(m => m.id === matterId);
    if (!matter) return;
    
    const chatToAdd = filteredHistory.find(chat => chat.id === chatId);
    if (!chatToAdd) return;
    
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        return {
          ...currentData,
          folders: currentData.folders.map(matter => {
            if (matter.id === matterId) {
              return {
                ...matter,
                chats: [...matter.chats, chatToAdd]
              };
            }
            return matter;
          })
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch("/api/chat-org/folders/chats", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatId, folderName: matter.name }),
      });
      
      if (!response.ok) throw new Error("Failed to add chat to matter");
      
      // Revalidate to get actual data
      mutate();
      // toast.success("Chat added to matter successfully");
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to add chat to matter");
    }
  };

  const handleRemoveChatFromMatter = async (matterId: string, chatId: string) => {
    // Find the matter name
    const matter = matters.find(m => m.id === matterId);
    if (!matter) return;
    
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        return {
          ...currentData,
          folders: currentData.folders.map(matter => {
            if (matter.id === matterId) {
              return {
                ...matter,
                chats: matter.chats.filter(chat => chat.id !== chatId)
              };
            }
            return matter;
          })
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch(`/api/chat-org/folders/chats?chatId=${encodeURIComponent(chatId)}&folderName=${encodeURIComponent(matter.name)}`, {
        method: "DELETE",
      });
      
      if (!response.ok) throw new Error("Failed to remove chat from matter");
      
      // Revalidate to get actual data
      mutate();
      // toast.success("Chat removed from matter successfully");
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to remove chat from matter");
    }
  };

  const handleReorderChatsInMatter = async (matterId: string, chatIds: string[]) => {
    // Find the folder name
    const matter = matters.find(m => m.id === matterId);
    if (!matter) return;
    
    // Optimistically update using mutate
    mutate(
      currentData => {
        if (!currentData) return currentData;
        
        return {
          ...currentData,
          folders: currentData.folders.map(matter => {
            if (matter.id === matterId) {
              // Create a map of chats by ID for quick lookup
              const chatMap = new Map(
                matter.chats.map(chat => [chat.id, chat])
              );
              
              // Create a new array of chats in the desired order
              const reorderedChats = chatIds
                .map(id => chatMap.get(id))
                .filter((chat): chat is Chat => chat !== undefined);
              
              return {
                ...matter,
                chats: reorderedChats
              };
            }
            return matter;
          })
        };
      },
      false // Don't revalidate immediately
    );
    
    // Call API
    try {
      const response = await fetch("/api/chat-org/folders/chats", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatIds, folderName: matter.name }),
      });
      
      if (!response.ok) throw new Error("Failed to reorder chats in matter");
      
      // Revalidate to get actual data
      mutate();
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      toast.error("Failed to reorder chats in matter");
    }
  };

  // Add this state variable near the other state variables in SidebarHistory
  const [viewMode, setViewMode] = useState<"all" | "pinned" | "matters">("all");

  if (filteredHistory?.length === 0 && searchQuery) {
    return (
      <SidebarGroup>
        <SidebarGroupContent>
          <div className="px-1 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2">
            No chats found matching &ldquo;{searchQuery}&rdquo;
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  if (filteredHistory?.length === 0) {
    return (
      <SidebarGroup>
        <SidebarGroupContent>
          <div className="px-1 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2">
            Your conversations will appear here once you start chatting!
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  if (isLoading) {
    return (
      <SidebarGroup>
        <div className="px-1 py-1 text-xs text-sidebar-foreground/50">
          Today
        </div>
        <SidebarGroupContent>
          <div className="flex flex-col">
            {[44, 32, 28, 64, 52].map((item) => (
              <div
                key={item}
                className="rounded-md h-8 flex gap-2 px-1 items-center"
              >
                <div
                  className="h-4 rounded-md flex-1 max-w-[--skeleton-width] bg-sidebar-accent-foreground/10"
                  style={
                    {
                      "--skeleton-width": `${item}%`,
                    } as React.CSSProperties
                  }
                />
              </div>
            ))}
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  const groupChatsByDate = (chats: Chat[]): GroupedChats => {
    const now = new Date();
    const oneWeekAgo = subWeeks(now, 1);
    const oneMonthAgo = subMonths(now, 1);

    // First sort all chats by updatedAt (or createdAt if updatedAt is not available)
    const sortedChats = [...chats].sort((a, b) => {
      const dateA = a.updatedAt || a.createdAt;
      const dateB = b.updatedAt || b.createdAt;
      return new Date(dateB).getTime() - new Date(dateA).getTime();
    });

    return sortedChats.reduce(
      (groups, chat) => {
        const chatDate = new Date(chat.updatedAt || chat.createdAt);

        if (isToday(chatDate)) {
          groups.today.push(chat);
        } else if (isYesterday(chatDate)) {
          groups.yesterday.push(chat);
        } else if (chatDate > oneWeekAgo) {
          groups.lastWeek.push(chat);
        } else if (chatDate > oneMonthAgo) {
          groups.lastMonth.push(chat);
        } else {
          groups.older.push(chat);
        }

        return groups;
      },
      {
        today: [],
        yesterday: [],
        lastWeek: [],
        lastMonth: [],
        older: [],
      } as GroupedChats
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {/* Add the organization component here */}
              {user && (
                <SidebarOrganization
                  allChats={filteredHistory || []}
                  pinnedChats={pinnedChats}
                  matters={matters}
                  onPinChat={handlePinChat}
                  onUnpinChat={handleUnpinChat}
                  onReorderPins={handleReorderPins}
                  onAddMatter={handleAddMatter}
                  onRenameMatter={handleRenameMatter}
                  onDeleteMatter={handleDeleteMatter}
                  onAddChatToMatter={handleAddChatToMatter}
                  onRemoveChatFromMatter={handleRemoveChatFromMatter}
                  onReorderChatsInMatter={handleReorderChatsInMatter}
                  setOpenMobile={setOpenMobile}
                  viewMode={viewMode}
                  setViewMode={setViewMode}
                  onDeleteChat={(chatId) => {
                    setDeleteId(chatId);
                    setShowDeleteDialog(true);
                  }}
                  onBulkDelete={() => setShowBulkDeleteDialog(true)}
                />
              )}
              
              {/* Then your existing grouped chats rendering */}
              {viewMode === "all" && filteredHistory &&
                (() => {
                  const groupedChats = groupChatsByDate(filteredHistory);

                  return (
                    <>
                      {groupedChats.today.length > 0 && (
                        <>
                          <div className="px-1 py-1 text-xs text-sidebar-foreground/50">
                            Today
                          </div>
                          {groupedChats.today.map((chat) => (
                            <ChatItem
                              key={chat.id}
                              chat={chat}
                              isActive={chat.id === id}
                              onDelete={(chatId) => {
                                setDeleteId(chatId);
                                setShowDeleteDialog(true);
                              }}
                              onPinChat={handlePinChat}
                              onUnpinChat={handleUnpinChat}
                              setOpenMobile={setOpenMobile}
                              onBulkDelete={() => setShowBulkDeleteDialog(true)}
                              pinnedChats={sidebarData?.pinnedChats || []}
                              onAddToFolder={(chatId, folderId) => {
                                const matter = matters.find(m => m.id === folderId);
                                if (matter) {
                                  handleAddChatToMatter(folderId, chatId);
                                }
                              }}
                              onManageTags={() => openTagDialog(chat)}
                            />
                          ))}
                        </>
                      )}

                      {groupedChats.yesterday.length > 0 && (
                        <>
                          <div className="px-1 py-1 text-xs text-sidebar-foreground/50 mt-0">
                            Yesterday
                          </div>
                          {groupedChats.yesterday.map((chat) => (
                            <ChatItem
                              key={chat.id}
                              chat={chat}
                              isActive={chat.id === id}
                              onDelete={(chatId) => {
                                setDeleteId(chatId);
                                setShowDeleteDialog(true);
                              }}
                              onPinChat={handlePinChat}
                              onUnpinChat={handleUnpinChat}
                              setOpenMobile={setOpenMobile}
                              onBulkDelete={() => setShowBulkDeleteDialog(true)}
                              pinnedChats={sidebarData?.pinnedChats || []}
                              onAddToFolder={(chatId, folderId) => {
                                const matter = matters.find(m => m.id === folderId);
                                if (matter) {
                                  handleAddChatToMatter(folderId, chatId);
                                }
                              }}
                              onManageTags={() => openTagDialog(chat)}
                            />
                          ))}
                        </>
                      )}

                      {groupedChats.lastWeek.length > 0 && (
                        <>
                          <div className="px-1 py-1 text-xs text-sidebar-foreground/50 mt-6">
                            Last 14 days
                          </div>
                          {groupedChats.lastWeek.map((chat) => (
                            <ChatItem
                              key={chat.id}
                              chat={chat}
                              isActive={chat.id === id}
                              onDelete={(chatId) => {
                                setDeleteId(chatId);
                                setShowDeleteDialog(true);
                              }}
                              onPinChat={handlePinChat}
                              onUnpinChat={handleUnpinChat}
                              setOpenMobile={setOpenMobile}
                              onBulkDelete={() => setShowBulkDeleteDialog(true)}
                              pinnedChats={sidebarData?.pinnedChats || []}
                              onAddToFolder={(chatId, folderId) => {
                                const matter = matters.find(m => m.id === folderId);
                                if (matter) {
                                  handleAddChatToMatter(folderId, chatId);
                                }
                              }}
                              onManageTags={() => openTagDialog(chat)}
                            />
                          ))}
                        </>
                      )}

                      {groupedChats.lastMonth.length > 0 && (
                        <>
                          <div className="px-1 py-1 text-xs text-sidebar-foreground/50 mt-6">
                            Last 30 days
                          </div>
                          {groupedChats.lastMonth.map((chat) => (
                            <ChatItem
                              key={chat.id}
                              chat={chat}
                              isActive={chat.id === id}
                              onDelete={(chatId) => {
                                setDeleteId(chatId);
                                setShowDeleteDialog(true);
                              }}
                              onPinChat={handlePinChat}
                              onUnpinChat={handleUnpinChat}
                              setOpenMobile={setOpenMobile}
                              onBulkDelete={() => setShowBulkDeleteDialog(true)}
                              pinnedChats={sidebarData?.pinnedChats || []}
                              onAddToFolder={(chatId, folderId) => {
                                const matter = matters.find(m => m.id === folderId);
                                if (matter) {
                                  handleAddChatToMatter(folderId, chatId);
                                }
                              }}
                              onManageTags={() => openTagDialog(chat)}
                            />
                          ))}
                        </>
                      )}

                      {groupedChats.older.length > 0 && (
                        <>
                          <div className="px-1 py-1 text-xs text-sidebar-foreground/50 mt-6">
                            Older
                          </div>
                          {groupedChats.older.map((chat) => (
                            <ChatItem
                              key={chat.id}
                              chat={chat}
                              isActive={chat.id === id}
                              onDelete={(chatId) => {
                                setDeleteId(chatId);
                                setShowDeleteDialog(true);
                              }}
                              onPinChat={handlePinChat}
                              onUnpinChat={handleUnpinChat}
                              setOpenMobile={setOpenMobile}
                              onBulkDelete={() => setShowBulkDeleteDialog(true)}
                              pinnedChats={sidebarData?.pinnedChats || []}
                              onAddToFolder={(chatId, folderId) => {
                                const matter = matters.find(m => m.id === folderId);
                                if (matter) {
                                  handleAddChatToMatter(folderId, chatId);
                                }
                              }}
                              onManageTags={() => openTagDialog(chat)}
                            />
                          ))}
                        </>
                      )}
                    </>
                  );
                })()}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete your
                chat and remove it from our database.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
        <BulkDeleteDialog
          isOpen={showBulkDeleteDialog}
          onClose={() => setShowBulkDeleteDialog(false)}
          chats={filteredHistory}
          onDelete={handleBulkDelete}
        />

        {/* Add tag management dialog */}
        {isDialogOpen && currentChat && (
          <TagManagementDialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              if (!open) closeTagDialog();
            }}
            chat={currentChat}
            allChats={sidebarData?.chats || []}
            onSave={handleSaveTags}
            isLoading={isTagsLoading}
            lightweightDialog={true}
            anchorElement={document.activeElement as HTMLElement}
          />
        )}
      </>
    </DndProvider>
  );
}
