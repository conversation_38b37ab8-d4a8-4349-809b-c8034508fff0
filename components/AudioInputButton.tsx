"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import { Button } from "./ui/button";
import { MicrophoneIcon } from "./icons";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { capitalizeFirstLetter } from "@/lib/utils";

declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
  }
}

interface ISpeechRecognition {
  start: () => void;
  stop: () => void;
  onresult: (event: any) => void;
  onerror: (event: any) => void;
  onstart: () => void;
  onend: () => void;
  continuous: boolean;
  interimResults: boolean;
  lang: string;
}

function AudioInputButton({
  setInput,
  isLoading,
  textareaRef,
}: {
  setInput: (value: string) => void;
  isLoading: boolean;
  textareaRef: React.RefObject<HTMLTextAreaElement>;
}) {
  const [isListening, setIsListening] = useState(false);
  const [permissionState, setPermissionState] =
    useState<PermissionState | null>(null);
  const recognitionRef = useRef<ISpeechRecognition | null>(null);

  // Check permission on mount
  useEffect(() => {
    const checkMicrophonePermission = async () => {
      try {
        // Check if permissions API is supported
        if (navigator.permissions && navigator.permissions.query) {
          const permissionStatus = await navigator.permissions.query({
            name: "microphone" as PermissionName,
          });
          setPermissionState(permissionStatus.state);

          // Listen for permission changes
          permissionStatus.onchange = () => {
            setPermissionState(permissionStatus.state);
          };
        }
      } catch (error) {
        console.error("Error checking microphone permission:", error);
        toast.error("Failed to check microphone permissions");
      }
    };

    checkMicrophonePermission();
  }, []);

  const requestMicrophoneAccess = async (): Promise<boolean> => {
    try {
      // This will trigger the permission prompt if not already granted
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // If we get here, permission was granted
      // We can stop the tracks immediately since we just needed permission
      stream.getTracks().forEach((track) => track.stop());
      return true;
    } catch (error) {
      console.error("Microphone permission error:", error);
      toast.error(
        "Microphone access denied. Please enable permissions in your browser settings."
      );
      return false;
    }
  };

  const startListening = useCallback(async () => {
    if (isListening) return;

    // First request microphone permission
    const hasPermission = await requestMicrophoneAccess();
    if (!hasPermission) return;

    // Check if browser supports SpeechRecognition
    if (
      typeof window === "undefined" ||
      (!window.webkitSpeechRecognition && !window.SpeechRecognition)
    ) {
      toast.error("Speech recognition is not supported in your browser");
      return;
    }

    try {
      // Create the recognition instance safely
      const SpeechRecognitionAPI =
        window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognitionAPI();
      recognitionRef.current = recognition;

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = "en-US";

      recognition.onstart = () => {
        setIsListening(true);
        toast.success("Listening...");
      };

      recognition.onresult = (event: any) => {
        // Convert results to transcript
        const results = Array.from(event.results);
        const transcript = results
          .map((result: any) => result[0].transcript)
          .join("");

        // Capitalize the transcript
        const capitalizedTranscript = capitalizeFirstLetter(transcript);

        // Update the input value with capitalized text
        setInput(capitalizedTranscript);

        // Update the textarea value for visual feedback
        if (textareaRef.current) {
          textareaRef.current.value = capitalizedTranscript;
          // Adjust height to match content
          textareaRef.current.style.height = "auto";
          textareaRef.current.style.height = `${
            textareaRef.current.scrollHeight + 2
          }px`;
        }
      };

      recognition.onerror = (event: any) => {
        console.error("Speech recognition error", event.error);
        if (event.error === "not-allowed") {
          toast.error(
            "Microphone access denied. Please enable permissions in your browser settings."
          );
        } else if (event.error === "network") {
          toast.error("Network error. Please check your internet connection.");
        } else if (event.error === "no-speech") {
          toast.warning("No speech detected. Please try speaking again.");
        } else if (event.error === "aborted") {
          toast.warning("Speech recognition was aborted");
        } else {
          toast.error(`Speech recognition error: ${event.error}`);
        }
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognition.start();
    } catch (error) {
      console.error("Failed to start speech recognition:", error);
      toast.error("Failed to start speech recognition");
      setIsListening(false);
    }
  }, [isListening, setInput, textareaRef]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
        toast.success("Stopped listening");
      } catch (error) {
        console.error("Error stopping speech recognition:", error);
        toast.error("Error stopping speech recognition");
      }
    }
    setIsListening(false);
  }, []);

  const toggleListening = useCallback(
    (e: React.MouseEvent) => {
      // Prevent default to avoid form submission
      e.preventDefault();

      if (isListening) {
        stopListening();
      } else {
        startListening();
      }
    },
    [isListening, startListening, stopListening]
  );

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className={`
            rounded-md rounded-bl-lg p-[7px] h-fit 
            dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200
            ${isListening ? "bg-red-500 hover:bg-red-600 text-white" : ""}
            ${permissionState === "denied" ? "bg-gray-400 hover:bg-gray-500 text-white" : ""}
          `}
          onClick={toggleListening}
          disabled={isLoading || permissionState === "denied"}
          variant="ghost"
          type="button" // Explicitly set button type to prevent form submission
        >
          <MicrophoneIcon size={14} />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        {permissionState === "denied"
          ? "Microphone access denied"
          : isListening
          ? "Stop Transcription"
          : "Start voice input"}
      </TooltipContent>
    </Tooltip>
  );
}

export default React.memo(AudioInputButton);
