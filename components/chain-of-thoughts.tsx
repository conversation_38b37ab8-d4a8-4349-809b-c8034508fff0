"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Card } from "./ui/card";
import { CheckCircle } from "lucide-react";

interface ThinkingStep {
  id: number;
  text: string;
  completed: boolean;
}

interface ThoughtItem {
  text: string;
}

interface ChainOfThoughtsProps {
  rewrittenQuery?: string;
  steps?: ThinkingStep[];
  thoughts?: ThoughtItem[];
  currentStep?: number;
}

export function ChainOfThoughts({
  steps = [
    { id: 1, text: "Analyzing your question", completed: false },
    { id: 2, text: "Gathering relevant information", completed: false },
    { id: 3, text: "Organizing key concepts", completed: false },
    { id: 4, text: "Formulating final response", completed: false },
  ],
  currentStep = 3,
}: ChainOfThoughtsProps) {
  const progressPercentage = (currentStep / steps.length) * 100;
  const activeStep = steps.find(step => step.id === currentStep) || steps[0];
  
  return (
    <div className="rounded-lg border bg-background p-6 shadow-sm w-full">
      <div className="flex items-center mb-4">
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border mr-3">
          <span className="text-sm">🔄</span>
        </div>
        <span className="text-lg font-medium">Thinking...</span>
      </div>

      <div className="w-full bg-secondary h-1.5 rounded-full mb-6 overflow-hidden">
        <div 
          className="animate-shimmer h-1.5 rounded-full transition-all duration-700 ease-in-out" 
          style={{ 
            width: `${progressPercentage}%`,
            boxShadow: '0 0 8px rgba(var(--primary), 0.5)',
            background: 'linear-gradient(90deg, hsl(var(--primary) / 0.7) 0%, hsl(var(--primary)) 50%, hsl(var(--primary) / 0.7) 100%)'
          }}
        ></div>
      </div>

      <div className="flex items-center gap-2 mb-4">
        <CheckCircle className="h-5 w-5 text-primary" />
        <span className="text-shimmer font-medium">
          {activeStep.text}
        </span>
        <span className="animate-dots ml-1">
          <span className="h-1.5 w-1.5 rounded-full bg-foreground inline-block"></span>
          <span className="h-1.5 w-1.5 rounded-full bg-foreground inline-block mx-0.5"></span>
          <span className="h-1.5 w-1.5 rounded-full bg-foreground inline-block"></span>
        </span>
        {currentStep < steps.length && <span className="text-xs text-muted-foreground ml-auto">{Math.round(progressPercentage)}%</span>}
      </div>
    </div>
  );
} 