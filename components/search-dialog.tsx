"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useDebounceCallback } from "@/hooks/use-debounce";
import { SearchResult } from "@/lib/types";
import { useRouter } from "next/navigation";
import { SearchResultItem } from "@/components/search-result-item";

export function SearchDialog({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const lastRequestTime = useRef<number>(0);

  const fetchSearchResults = async (query: string) => {
    const requestTime = Date.now();
    lastRequestTime.current = requestTime;
    
    setIsSearching(true);
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
      const data = await response.json();
      
      // Only update results if this is still the most recent request
      if (requestTime === lastRequestTime.current) {
        setResults(data);
      }
    } catch (error) {
      console.error("Search failed:", error);
      // Only clear results if this is still the most recent request
      if (requestTime === lastRequestTime.current) {
        setResults([]);
      }
    } finally {
      // Only update loading state if this is still the most recent request
      if (requestTime === lastRequestTime.current) {
        setIsSearching(false);
      }
    }
  };

  // Load initial results when dialog opens
  useEffect(() => {
    if (isOpen && !searchQuery && results.length === 0) {
      fetchSearchResults("");
    }
  }, [isOpen]);

  const debouncedSearch = useDebounceCallback(fetchSearchResults, 300);

  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value);
    debouncedSearch(value);
  }, [debouncedSearch]);

  const handleResultSelect = (chatId: string) => {
    router.push(`/chat/${chatId}`);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <div className="flex flex-col gap-4">
          <Input
            placeholder="Search your chats..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full"
            autoFocus
          />
          <div className="max-h-[400px] overflow-y-auto">
            {isSearching ? (
              <div className="text-center py-4 text-muted-foreground">
                Searching...
              </div>
            ) : results.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                No results found
              </div>
            ) : (
              <div className="flex flex-col divide-y divide-border">
                {results.map((result) => (
                  <SearchResultItem
                    key={`${result.chatId}-${result.messageId ?? 'title'}`}
                    result={result}
                    onSelect={() => handleResultSelect(result.chatId)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
