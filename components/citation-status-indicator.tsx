import React from "react";
import { Loader2, CheckCircle, XCircle, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

interface CitationStatus {
  status: "pending" | "processing" | "completed" | "failed";
  startedAt?: string;
  completedAt?: string;
  error?: string;
  citationCount?: number;
  processingTimeMs?: number;
}

interface CitationStatusIndicatorProps {
  status: CitationStatus | null;
  className?: string;
  showText?: boolean;
  size?: "sm" | "md" | "lg";
}

export function CitationStatusIndicator({
  status,
  className,
  showText = true,
  size = "sm",
}: CitationStatusIndicatorProps) {
  if (!status) {
    return null; // Only return null if no status at all
  }

  const sizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  const getStatusIcon = () => {
    switch (status.status) {
      case "pending":
        return <Clock className={cn(sizeClasses[size], "text-yellow-500")} />;
      case "processing":
        return <Loader2 className={cn(sizeClasses[size], "text-blue-500 animate-spin")} />;
      case "completed":
        return <CheckCircle className={cn(sizeClasses[size], "text-green-500")} />;
      case "failed":
        return <XCircle className={cn(sizeClasses[size], "text-red-500")} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status.status) {
      case "pending":
        return "Preparing citations...";
      case "processing":
        return "Adding citations...";
      case "completed":
        const count = status.citationCount || 0;
        return count > 0 ? `Citations added (${count})` : "Citations processed";
      case "failed":
        return "Citation processing failed";
      default:
        return "";
    }
  };

  const getStatusColor = () => {
    switch (status.status) {
      case "pending":
        return "text-yellow-600 dark:text-yellow-400";
      case "processing":
        return "text-blue-600 dark:text-blue-400";
      case "completed":
        return "text-green-600 dark:text-green-400";
      case "failed":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  return (
    <div className={cn(
      "flex items-center gap-1.5 px-2 py-1 rounded-md bg-muted/50 border",
      className
    )}>
      {getStatusIcon()}
      {showText && (
        <span className={cn(
          textSizeClasses[size],
          getStatusColor(),
          "font-medium"
        )}>
          {getStatusText()}
        </span>
      )}
    </div>
  );
}

interface CitationBadgeProps {
  citationCount: number;
  className?: string;
}

export function CitationBadge({ citationCount, className }: CitationBadgeProps) {
  if (citationCount === 0) return null;

  return (
    <div className={cn(
      "inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium",
      className
    )}>
      <CheckCircle className="h-3 w-3" />
      {citationCount} {citationCount === 1 ? "citation" : "citations"}
    </div>
  );
}
