"use client";

import type { User } from "next-auth";
import { useRouter, usePathname } from "next/navigation";
import { useState, useEffect, useCallback, useRef } from "react";
import { RefreshCw, Database } from "lucide-react";
import { SidebarHistory } from "@/components/sidebar-history";
import { SidebarUserNav } from "@/components/sidebar-user-nav";
import { Button } from "@/components/ui/button";
import { DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { fetcher } from "@/lib/utils";
import { UsageTracker } from "@/components/account/UsageTracker";
import { cn } from "@/lib/utils";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
  SidebarInput,
} from "@/components/ui/sidebar";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { SearchDialog } from "@/components/search-dialog";
import { PageLoader } from "@/components/ui/page-loader";
import { useTheme } from "next-themes";
import { PlusIcon, SearchIcon } from "./icons";

export function AppSidebar({ user }: { user: User | undefined }) {
  const router = useRouter();
  const { setOpenMobile, state } = useSidebar();
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { resolvedTheme } = useTheme();
  const pathname = usePathname();
  const [usageData, setUsageData] = useState({
    queries: 0,
    queryLimit: 10,
    usageStats: {
      dailyMessageCount: 0,
      totalChats: 0,
      memoryUsageMB: 0,
      dailyMessageLimit: 10,
      totalChatLimit: 10,
      memoryLimit: 150,
    },
  });
  const [isLoadingUsage, setIsLoadingUsage] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(256); // 16rem = 256px default
  const [isResizing, setIsResizing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const resizeRef = useRef<HTMLDivElement>(null);
  const minWidth = 180; // Minimum sidebar width
  const maxWidth = 400; // Maximum sidebar width

  // Only fetch usage data for non-admin free tier users
  const fetchUsageData = useCallback(async () => {
    if (!user || user.isAdmin || user.subscriptionTier !== 'free') {
      setIsLoadingUsage(false);
      return;
    }

    try {
      const usageStats = await fetcher("/api/usage/stats");
      if (usageStats) {
        setUsageData({
          queries: usageStats.dailyMessageCount || 0,
          queryLimit: usageStats.dailyMessageLimit || 10,
          usageStats: {
            dailyMessageCount: usageStats.dailyMessageCount || 0,
            totalChats: usageStats.totalChats || 0,
            memoryUsageMB: usageStats.memoryUsageMB || 0,
            dailyMessageLimit: usageStats.dailyMessageLimit || 10,
            totalChatLimit: usageStats.totalChatLimit || 10,
            memoryLimit: usageStats.memoryLimit || 150,
          },
        });
      }
    } catch (error) {
      console.error("Error fetching usage data:", error);
    } finally {
      setIsLoadingUsage(false);
    }
  }, [user]);

  // Initial fetch when component mounts
  useEffect(() => {
    fetchUsageData();
  }, [fetchUsageData]);

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  };

  // Handle resize end
  useEffect(() => {
    const handleResizeEnd = () => {
      setIsResizing(false);
    };

    const handleResize = (e: MouseEvent) => {
      if (!isResizing) return;
      
      // Use requestAnimationFrame for smoother resizing
      requestAnimationFrame(() => {
        // Calculate new width based on mouse position
        let newWidth = e.clientX;
        
        // Constrain width within min and max values
        newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
        
        setSidebarWidth(newWidth);
      });
    };

    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', handleResizeEnd);
    
    return () => {
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing]);

  // Check if the current route is the pricing page
  const isPricingPage = pathname === "/subscription";
  const isWelcomePage = pathname === "/welcome";
  const isAccountPage = pathname === "/account";

  // Don't render the sidebar at all on the pricing page
  if (isPricingPage || isWelcomePage || isAccountPage) {
    return null;
  }

  // Check if user is on free plan
  const isFreePlan = user?.subscriptionTier === "free";

  // Add a function to handle manual refresh
  const handleRefreshUsage = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    await fetchUsageData();
    setIsRefreshing(false);
  };


  return (
    <>
    <Sidebar
      variant="floating"
      className="group-data-[side=left]:border-r-0 mt-0 ml-0 flex flex-col h-screen z-[49] bg-white dark:bg-background backdrop-blur-sm overflow-hidden p-0 md:w-auto w-full"
      style={{
        border: "1px solid rgba(var(--base-purple), 0.3)",
        borderRight:
          resolvedTheme === "dark"
            ? "1px solid var(--header-border-dark)"
            : "1px solid rgba(0, 0, 0, 0.1)",
        transition: "all 300ms cubic-bezier(0.25, 0.1, 0.25, 1)",
        width: `${sidebarWidth}px`,
        paddingTop: "71px",
        marginTop: "0",
      }}
    >
      {/* Improved resize handle with better visual indicators */}
      <div
        ref={resizeRef}
        className={cn(
          "absolute top-0 right-0 h-full w-6 cursor-ew-resize z-10",
          isResizing ? "bg-primary/20" : "hover:bg-primary/10",
          "transition-colors duration-150"
        )}
        onMouseDown={handleResizeStart}
        style={{ 
          height: "100%",
          transform: "translateX(3px)" 
        }}
      >
        {/* Three-dot vertical indicator for better visibility */}
        <div className="absolute top-1/2 right-1 -translate-y-1/2 flex flex-col gap-1.5 pointer-events-none">
          <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground/40" />
          <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground/40" />
          <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground/40" />
        </div>
      </div>
      <SidebarHeader className="flex-none px-2 pt-2 transition-all duration-300 ease-in-out">
        <SidebarMenu>
          <Tooltip>
            <TooltipContent side="bottom" sideOffset={5}>
              Search chats by content
            </TooltipContent>
          </Tooltip>
          <div className="px-0 mt-0">
            <div className="relative">
              <SidebarInput
                placeholder="Search chats by content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<SearchIcon />}
                // onFocus={() => setIsSearchOpen(true)}
                onClick={() => setIsSearchOpen(true)}
                autoFocus={false}
                tabIndex={-1}
                className="bg-gray-100 dark:bg-gray-800 text-black dark:text-white placeholder:text-gray-500"
              />
            </div>
          </div>
          <DropdownMenuSeparator />
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent
        className={`flex-1 overflow-auto bg-transparent px-2 transition-all duration-300 ease-in-out ${
          state === "collapsed" ? "scrollbar-hide" : ""
        }`}
      >
        <SidebarHistory user={user} searchQuery={searchQuery} />
      </SidebarContent>
      <SidebarFooter className="flex-none bg-transparent px-2 pb-2 transition-all duration-300 ease-in-out">
        {/* Only show usage stats for free plan users */}
        {user && isFreePlan && (
          <div className="mb-4 px-2 py-4 rounded-md">
            <div className="px-4 py-3 bg-zinc-100 dark:bg-zinc-800/70 rounded-md">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium">Preview Usage Limits</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefreshUsage}
                  disabled={isRefreshing}
                  className="h-6 w-6 p-0"
                >
                  <RefreshCw className={`h-3.5 w-3.5 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span className="sr-only">Refresh usage stats</span>
                </Button>
              </div>
              <UsageTracker
                queries={usageData.queries}
                queryLimit={usageData.queryLimit}
                isLoading={isLoadingUsage}
                usageStats={usageData.usageStats}

              />
            </div>
          </div>
        )}

        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <DropdownMenuSeparator />
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                className="w-full text-sm h-9 flex items-center gap-1 transition-colors hover:bg-primary hover:text-primary-foreground"
                onClick={() => {
                  setOpenMobile(false);
                  router.push("/");
                  router.refresh();
                }}
              >
                <PlusIcon />
                New Chat
              </Button>
            </TooltipTrigger>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                className="w-full text-sm h-9 flex items-center gap-1 transition-colors hover:bg-primary hover:text-primary-foreground"
                onClick={() => {
                  setOpenMobile(false);
                  setIsLoading(true);
                  router.push("/library");
                }}
              >
                <Database />
                Library
              </Button>
            </TooltipTrigger>
            <DropdownMenuSeparator />
          </Tooltip>
        </TooltipProvider>
        {user && <SidebarUserNav user={user} />}
      </SidebarFooter>
      <SearchDialog
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </Sidebar>
    {isLoading && <PageLoader message="Loading..." />}
    </>
  );
}
