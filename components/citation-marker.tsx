'use client';

import React, { useEffect, useState, useRef } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { CitationMatch } from '@/lib/utils/citation-formatter';
import { FileText } from 'lucide-react';

interface CitationMarkerProps {
  citationNumber: number;
  citation: CitationMatch | undefined;
  referenceText?: string;
}

// Simple citation markers - using [1], [2], [3] format for better reliability
const getSimpleCitationMarker = (num: number): string => {
  return `[${num}]`;
};

export function CitationMarker({ citationNumber, citation, referenceText }: CitationMarkerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-close popover on scroll for better UX
  useEffect(() => {
    const handleScroll = () => {
      if (isOpen) {
        // Clear any existing timeout
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }

        // Set a small delay before closing to avoid immediate closure on minor scrolls
        scrollTimeoutRef.current = setTimeout(() => {
          setIsOpen(false);
        }, 150);
      }
    };

    if (isOpen) {
      // Listen for scroll events on window and any scrollable containers
      window.addEventListener('scroll', handleScroll, { passive: true });
      document.addEventListener('scroll', handleScroll, { passive: true });

      // Also listen for wheel events for better responsiveness
      window.addEventListener('wheel', handleScroll, { passive: true });
    }

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('scroll', handleScroll);
      window.removeEventListener('wheel', handleScroll);

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [isOpen]);

  // Debug logging to trace citation data (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("🔗 CitationMarker rendered", {
        citationNumber,
        hasCitation: !!citation,
        hasReferenceText: !!referenceText,
        citationData: citation ? {
          filename: citation.filename,
          pageNumber: citation.pageNumber,
          chunkPreview: citation.chunkPreview?.substring(0, 50) + '...',
          chunkId: citation.chunkId,
          similarity: citation.similarity,
          sourceDocumentId: citation.sourceDocumentId,
          fullCitation: citation
        } : null,
        referenceText: referenceText?.substring(0, 50) + '...',
        renderingMode: citation ? 'WITH_POPOVER' : 'FALLBACK_ONLY'
      });
    }
  }, [citationNumber, citation, referenceText]);

  // Get the citation marker
  const marker = getSimpleCitationMarker(citationNumber);

  // Build comprehensive citation content for popover
  const buildCitationContent = () => {
    // Priority 1: Use reference text if available
    if (referenceText && referenceText.trim()) {
      return {
        snippet: referenceText.trim(),
        source: citation?.filename || 'Unknown Document',
        page: citation?.pageNumber
      };
    }

    // Priority 2: Build from citation data
    if (citation) {
      let snippet = '';

      // Get the best available text snippet
      if (citation.chunkPreview && citation.chunkPreview.trim()) {
        snippet = citation.chunkPreview.trim();
      } else if (citation.chunkText && citation.chunkText.trim()) {
        snippet = citation.chunkText.trim();
      }

      return {
        snippet: snippet || 'No preview available',
        source: citation.filename || 'Unknown Document',
        page: citation.pageNumber
      };
    }

    // Fallback
    return {
      snippet: 'Reference data not available',
      source: 'Unknown Document',
      page: undefined
    };
  };

  const citationContent = buildCitationContent();

  // If no citation data is available, still render the marker but without popover
  if (!citation) {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`CitationMarker: No citation data for number ${citationNumber}`, {
        citationNumber,
        hasReferenceText: !!referenceText
      });
    }

    // Still render the citation marker even without citation data
    return (
      <span
        className="inline-block text-gray-500 dark:text-gray-400 font-medium border border-gray-300 dark:border-gray-600 px-1 py-0.5 rounded-sm text-xs bg-gray-50 dark:bg-gray-900/20 align-baseline leading-none"
        title={`Citation ${citationNumber} - Reference data not available`}
        style={{
          verticalAlign: 'baseline',
          lineHeight: '1',
          margin: '0',
          padding: '1px 3px',
          fontSize: '0.75rem'
        }}
      >
        {marker}
      </span>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <span
          className="inline-block text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200 cursor-pointer no-underline font-medium border border-blue-300 dark:border-blue-600 px-1 py-0.5 rounded-sm text-xs bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 align-baseline leading-none"
          role="button"
          tabIndex={0}
          aria-label={`Citation ${citationNumber}: ${citation.filename || 'Unknown source'}`}
          style={{
            verticalAlign: 'baseline',
            lineHeight: '1',
            margin: '0',
            padding: '1px 3px',
            fontSize: '0.75rem'
          }}
          onClick={(e) => {
            e.preventDefault();
            setIsOpen(!isOpen);
            if (process.env.NODE_ENV === 'development') {
              console.log("🔗 Citation clicked:", { citationNumber, citation, isOpen: !isOpen });
            }
          }}
        >
          {marker}
        </span>
      </PopoverTrigger>
      <PopoverContent
        side="top"
        className="w-80 p-4"
        sideOffset={8}
      >
        <div className="space-y-3">
          {/* Citation snippet */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <FileText className="h-4 w-4" />
              <span>Citation {citationNumber}</span>
            </div>
            <div className="text-sm leading-relaxed bg-muted/50 p-3 rounded-md border-l-2 border-blue-500">
              &ldquo;{citationContent.snippet.length > 300
                ? citationContent.snippet.substring(0, 300) + '...'
                : citationContent.snippet}&rdquo;
            </div>
          </div>

          {/* Source information */}
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex items-center justify-between">
              <span className="font-medium">Source:</span>
              <span className="text-right max-w-48 truncate">{citationContent.source}</span>
            </div>
            {citationContent.page && (
              <div className="flex items-center justify-between">
                <span className="font-medium">Page:</span>
                <span>{citationContent.page}</span>
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
