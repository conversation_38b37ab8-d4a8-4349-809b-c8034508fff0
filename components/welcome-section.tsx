"use client";

import React from "react";
import { memo } from "react";
import WelcomeText from "./welcome-text";
import { VideoTutorials } from "./video-tutorials";
import { SuggestedActions } from "./suggested-actions";
import { SecurityFeatures } from "./security-features";

interface WelcomeSectionProps {
  chatId: string;
  setInput?: (value: string) => void;
  submitForm?: () => void;
}

const PureWelcomeSection = ({
  chatId,
  setInput,
  submitForm,
}: WelcomeSectionProps) => {
  return (
    <div className="w-full mx-auto sm:px-4 px-0 py-6 pt-0 flex flex-col overflow-y-auto lg:max-h-[calc(100vh-270px)] md:max-h-[calc(100vh-275px)] sm:max-h-[calc(100vh-300px)] max-h-[calc(100vh-310px)] hideScrollBar h-full xl:justify-center justify-between">
      <div className="text-center sticky md:top-[0px] top-[0px] chat-background-light dark:chat-background-dark sm:pt-[40px] pt-[0px] z-10">
        <WelcomeText />
      </div>

      <div className="flex flex-col lg:flex-row justify-center gap-4 mx-auto flex-wrap relative z-0">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-700 shadow-sm paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 sm:max-w-[280px] max-w-[250px] mx-auto md:mx-0">
          <VideoTutorials chatId={chatId} />
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-gray-100 dark:border-gray-700 paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 sm:max-w-[280px] max-w-[250px] mx-auto md:mx-0">
          {setInput && submitForm && (
            <SuggestedActions
              chatId={chatId}
              setInput={setInput}
              submitForm={submitForm}
            />
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-700 shadow-sm paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 sm:max-w-[280px] max-w-[250px] mx-auto md:mx-0">
          <SecurityFeatures chatId={chatId} />
        </div>
      </div>
    </div>
  );
};

export const WelcomeSection = memo(PureWelcomeSection);
