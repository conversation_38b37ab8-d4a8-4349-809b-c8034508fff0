/**
 * Lightweight Inline Citations Service
 * 
 * This service integrates with existing Enhanced Doc Reader and RAG services
 * through hooks to reuse extracted images for OCR processing without
 * modifying the original service implementations.
 */

import { Logger } from "../utils/Logger";
import { InlineCitationsHooks, ExtractedImageData } from "./hooks/inline-citations-hooks";

export interface LightweightCitationResult {
  success: boolean;
  originalContent: string;
  citedContent: string;
  citations: Array<{
    text: string;
    documentId: string;
    pageNumber?: number;
    confidence?: number;
  }>;
  citationCount: number;
  insertionLocations: number[];
  imagesSources: Array<{
    documentId: string;
    imageCount: number;
    source: 'enhanced-doc-reader' | 'rag' | 'manual';
  }>;
  error?: string;
}

export interface CitationOptions {
  maxCitations?: number;
  minConfidence?: number;
  useCache?: boolean;
  fallbackToExtraction?: boolean;
}

/**
 * Lightweight inline citations service that reuses images from other services
 */
export class LightweightInlineCitations {
  /**
   * Generate inline citations using cached images from Enhanced Doc Reader and RAG
   */
  static async generateInlineCitations(
    messageId: string,
    chatId: string,
    content: string,
    options: CitationOptions = {}
  ): Promise<LightweightCitationResult> {
    const startTime = Date.now();
    
    Logger.info("Starting lightweight inline citations generation", {
      messageId,
      chatId,
      contentLength: content.length,
      options
    });

    try {
      // Step 1: Get associated documents
      const associatedDocuments = await this.getAssociatedDocuments(messageId);
      
      if (associatedDocuments.length === 0) {
        Logger.info("No associated documents found for citation generation", {
          messageId,
          chatId
        });
        
        return {
          success: true,
          originalContent: content,
          citedContent: content,
          citations: [],
          citationCount: 0,
          insertionLocations: [],
          imagesSources: []
        };
      }

      // Step 2: Retrieve cached images from hooks
      const cachedImages = this.getCachedImagesForDocuments(
        associatedDocuments.map(doc => doc.id)
      );

      const imagesSources = Object.entries(cachedImages).map(([documentId, images]) => ({
        documentId,
        imageCount: images.length,
        source: this.detectImageSource(images)
      }));

      Logger.info("Retrieved cached images for citation generation", {
        messageId,
        documentCount: associatedDocuments.length,
        totalImages: Object.values(cachedImages).reduce((sum, images) => sum + images.length, 0),
        imagesSources
      });

      // Step 3: Process images for OCR if we have cached images
      if (Object.keys(cachedImages).length > 0) {
        const citationResult = await this.processImagesForCitations(
          content,
          cachedImages,
          options
        );

        const processingTime = Date.now() - startTime;
        
        Logger.info("Lightweight inline citations completed", {
          messageId,
          citationCount: citationResult.citationCount,
          processingTime,
          imagesSources
        });

        return {
          ...citationResult,
          imagesSources
        };
      }

      // Step 4: Fallback handling if no cached images
      if (options.fallbackToExtraction) {
        Logger.warn("No cached images found, falling back to extraction", {
          messageId,
          documentIds: associatedDocuments.map(doc => doc.id)
        });
        
        // This could trigger the original inline citations service as fallback
        return await this.fallbackToOriginalService(messageId, chatId, content);
      }

      // No images available and no fallback
      Logger.warn("No cached images available for citation generation", {
        messageId,
        documentCount: associatedDocuments.length
      });

      return {
        success: true,
        originalContent: content,
        citedContent: content,
        citations: [],
        citationCount: 0,
        insertionLocations: [],
        imagesSources: [],
        error: "No cached images available for citation processing"
      };

    } catch (error) {
      Logger.error("Error in lightweight inline citations generation", {
        messageId,
        chatId,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        originalContent: content,
        citedContent: content,
        citations: [],
        citationCount: 0,
        insertionLocations: [],
        imagesSources: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get cached images for multiple documents from the hooks system
   */
  private static getCachedImagesForDocuments(documentIds: string[]): Record<string, ExtractedImageData[]> {
    return InlineCitationsHooks.getCachedImagesForDocuments(documentIds);
  }

  /**
   * Detect the source of images based on metadata
   */
  private static detectImageSource(images: ExtractedImageData[]): 'enhanced-doc-reader' | 'rag' | 'manual' {
    if (images.length === 0) return 'manual';
    
    const firstImage = images[0];
    if (firstImage.metadata?.renderingApproach) {
      return 'enhanced-doc-reader';
    }
    if (firstImage.metadata?.source === 'rag') {
      return 'rag';
    }
    
    return 'manual';
  }

  /**
   * Process cached images for citation generation
   */
  private static async processImagesForCitations(
    content: string,
    cachedImages: Record<string, ExtractedImageData[]>,
    options: CitationOptions
  ): Promise<Omit<LightweightCitationResult, 'imagesSources'>> {
    try {
      // Convert cached images to format expected by OCR
      const ocrImages = this.convertCachedImagesToOcrFormat(cachedImages);
      
      if (ocrImages.length === 0) {
        return {
          success: true,
          originalContent: content,
          citedContent: content,
          citations: [],
          citationCount: 0,
          insertionLocations: []
        };
      }

      // Process images through OCR (simplified mock implementation)
      const ocrResults = await this.performOcrOnImages(ocrImages);
      
      // Generate citations from OCR results
      const citations = this.generateCitationsFromOcr(ocrResults, content, options);
      
      // Insert citations into content
      const { citedContent, insertionLocations } = this.insertCitationsIntoContent(content, citations);

      return {
        success: true,
        originalContent: content,
        citedContent,
        citations,
        citationCount: citations.length,
        insertionLocations
      };

    } catch (error) {
      Logger.error("Error processing images for citations", {
        error: error instanceof Error ? error.message : String(error),
        imageCount: Object.values(cachedImages).reduce((sum, images) => sum + images.length, 0)
      });

      return {
        success: false,
        originalContent: content,
        citedContent: content,
        citations: [],
        citationCount: 0,
        insertionLocations: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Convert cached images to OCR format
   */
  private static convertCachedImagesToOcrFormat(cachedImages: Record<string, ExtractedImageData[]>): Array<{
    documentId: string;
    pageNumber: number;
    imageData: string;
  }> {
    const ocrImages: Array<{ documentId: string; pageNumber: number; imageData: string }> = [];

    for (const [documentId, images] of Object.entries(cachedImages)) {
      for (const image of images) {
        ocrImages.push({
          documentId,
          pageNumber: image.pageNumber,
          imageData: image.imageBase64
        });
      }
    }

    return ocrImages;
  }

  /**
   * Perform OCR on images (simplified implementation)
   */
  private static async performOcrOnImages(images: Array<{ documentId: string; pageNumber: number; imageData: string }>): Promise<Array<{
    documentId: string;
    pageNumber: number;
    text: string;
    confidence: number;
  }>> {
    // This is a simplified mock implementation
    // In a real implementation, this would call an OCR service
    return images.map(image => ({
      documentId: image.documentId,
      pageNumber: image.pageNumber,
      text: `Extracted text from ${image.documentId} page ${image.pageNumber}`,
      confidence: 0.85
    }));
  }

  /**
   * Generate citations from OCR results
   */
  private static generateCitationsFromOcr(
    ocrResults: Array<{ documentId: string; pageNumber: number; text: string; confidence: number }>,
    content: string,
    options: CitationOptions
  ): Array<{ text: string; documentId: string; pageNumber: number; confidence: number }> {
    const minConfidence = options.minConfidence || 0.7;
    const maxCitations = options.maxCitations || 10;

    // Filter by confidence and limit count
    return ocrResults
      .filter(result => result.confidence >= minConfidence)
      .slice(0, maxCitations)
      .map(result => ({
        text: result.text.substring(0, 100), // Limit citation text length
        documentId: result.documentId,
        pageNumber: result.pageNumber,
        confidence: result.confidence
      }));
  }

  /**
   * Insert citations into content
   */
  private static insertCitationsIntoContent(
    content: string,
    citations: Array<{ text: string; documentId: string; pageNumber: number }>
  ): { citedContent: string; insertionLocations: number[] } {
    if (citations.length === 0) {
      return { citedContent: content, insertionLocations: [] };
    }

    // Simple implementation: add citations at the end
    const insertionLocations: number[] = [];
    let citedContent = content;

    citations.forEach((citation, index) => {
      const citationMarker = ` **[${index + 1}]**`;
      citedContent += citationMarker;
      insertionLocations.push(citedContent.length - citationMarker.length);
    });

    return { citedContent, insertionLocations };
  }

  /**
   * Get associated documents for a message
   */
  private static async getAssociatedDocuments(messageId: string): Promise<Array<{ id: string; filename: string }>> {
    try {
      // Import database modules
      const { db } = await import("../db/db");
      const { messageDocuments, sourceDocuments } = await import("../db/schema");
      const { eq } = await import("drizzle-orm");

      const documents = await db
        .select({
          id: sourceDocuments.id,
          filename: sourceDocuments.filename
        })
        .from(messageDocuments)
        .innerJoin(sourceDocuments, eq(messageDocuments.sourceDocumentId, sourceDocuments.id))
        .where(eq(messageDocuments.messageId, messageId));

      return documents.map(doc => ({
        id: doc.id,
        filename: doc.filename || 'Unknown Document'
      }));
    } catch (error) {
      Logger.error("Error getting associated documents", {
        messageId,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Fallback to original inline citations service
   */
  private static async fallbackToOriginalService(
    messageId: string,
    chatId: string,
    content: string
  ): Promise<LightweightCitationResult> {
    try {
      // Import and use the original service as fallback
      const { InlineCitationsGenerator } = await import("./inline-citations-generator");
      
      const result = await InlineCitationsGenerator.generateInlineCitations(
        messageId,
        chatId,
        content
      );

      return {
        success: result.success,
        originalContent: result.originalContent,
        citedContent: result.citedContent,
        citations: result.citations || [],
        citationCount: result.citationCount,
        insertionLocations: result.insertionLocations || [],
        imagesSources: [{ documentId: 'fallback', imageCount: 0, source: 'manual' }],
        error: result.error
      };
    } catch (error) {
      Logger.error("Fallback to original service failed", {
        messageId,
        chatId,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        originalContent: content,
        citedContent: content,
        citations: [],
        citationCount: 0,
        insertionLocations: [],
        imagesSources: [],
        error: "Fallback service failed"
      };
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): ReturnType<typeof InlineCitationsHooks.getCacheStats> {
    return InlineCitationsHooks.getCacheStats();
  }

  /**
   * Clear the image cache
   */
  static clearCache(): void {
    InlineCitationsHooks.clearCache();
  }
}
