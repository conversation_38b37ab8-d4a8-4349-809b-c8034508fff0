import { SearchService } from "./rag/SearchService";
import { PerplexityService } from "./rag/PerplexityService";
import { VoyageEmbeddingService } from "./rag/VoyageEmbeddingService";
import { SearchResult } from "./rag/types";
import { CONFIG } from "./rag/config";
import { Logger } from "../utils/Logger";
import { user } from "../db/schema";
import { QueryRewriteService } from "./rag/QueryRewriteService";

export class RAGProcessor {
  private static base64Cache: Map<string, string> = new Map();
  // static async processQuery(
  //   query: string,
  //   chatId: string,
  //   includeInternet: boolean = false,
  //   hasFiles: boolean = false,
  //   previousMessages: { role: string; content: any }[] = [],
  //   modelIdentifier: string,
  //   userMessageId?: string,
  //   finalMessageId?: string,
  //   filesText: string = "",
  //   saveMessageCallback?: (
  //     finalMessageId: string,
  //     chatId: string,
  //     content: string,
  //     metadata?: {
  //       internetResults?: {
  //         mainContent: string;
  //         citations: string[];
  //         formattedContent: string;
  //       };
  //       relevantImages?: Array<{ mime_type: string; data: string }>;
  //       rewrittenQuery?: string;
  //     }
  //   ) => Promise<void>
  // ): Promise<Response> {
  //   try {
  //     var relevantChunks: SearchResult[] = [];
  //     if (hasFiles && filesText.length == 0) {
  //       relevantChunks = await SearchService.searchEmbeddings(query, chatId);
  //     }

  //     const chatHistory = previousMessages
  //       .map((msg) => `${msg.role}: ${msg.content}`)
  //       .join("\n");

  //     const imageChunks =
  //       relevantChunks.length > 0
  //         ? RAGProcessor.extractImageChunks(relevantChunks)
  //         : [];

  //     const processedImages =
  //       imageChunks.length > 0
  //         ? await this.processImageChunks(imageChunks)
  //         : [];

  //     const rewrittenQuery = await QueryRewriteService.rewriteQuery(
  //       query,
  //       chatHistory,
  //       processedImages,
  //       filesText
  //     );

  //     Logger.debug("Chat History Summary:", rewrittenQuery);

  //     const perplexityAnswer = includeInternet
  //       ? await PerplexityService.getAnswer(rewrittenQuery)
  //       : { mainContent: "", citations: [], formattedContent: "" };

  //     if (includeInternet) {
  //       Logger.debug("Perplexity answer included:", perplexityAnswer);
  //     } else {
  //       Logger.debug("Perplexity answer skipped - Internet results disabled");
  //     }

  //     // const imageChunks = this.extractImageChunks(relevantChunks);

  //     return await GeminiService.getAnswer(
  //       rewrittenQuery, //replace w/ normal query if it gets too much.
  //       processedImages,
  //       perplexityAnswer,
  //       previousMessages,
  //       modelIdentifier,
  //       userMessageId,
  //       finalMessageId,
  //       chatId,
  //       saveMessageCallback
  //     );
  //   } catch (error) {
  //     Logger.error("Error in RAG process:", error);
  //     return new Response("Error processing your query", { status: 500 });
  //   }
  // }

  public static extractImageChunks(
    chunks: SearchResult[]
  ): Array<{ mime_type: string; data: string }> {
    return chunks
      .filter(
        (
          chunk
        ): chunk is SearchResult & {
          image_data: { mime_type: string; data: string };
        } => !!chunk.image_data
      )
      .map((chunk) => chunk.image_data);
  }

  // Adding a method to process image chunks in parallel
  public static async processImageChunks(
    imageChunks: Array<{ mime_type: string; data: string }>
  ): Promise<Array<{ mime_type: string; data: string; url: string }>> {
    // Filter out invalid chunks first
    const validChunks = imageChunks.filter(chunk => {
      if (!chunk || !chunk.data || typeof chunk.data !== 'string') {
        Logger.warn("Skipping invalid image chunk:", chunk);
        return false;
      }
      return true;
    });

    Logger.debug("Processing image chunks:", {
      totalChunks: imageChunks.length,
      validChunks: validChunks.length,
      skippedChunks: imageChunks.length - validChunks.length
    });

    // Processing all valid images in parallel
    const processedChunks = await Promise.all(
      validChunks.map(async (chunk) => {
        try {
          Logger.debug("Processing image chunk:", { mime_type: chunk.mime_type, dataLength: chunk.data.length });
          const base64Data = await this.convertUrlToBase64(chunk.data);

          if (!base64Data) {
            Logger.warn("Failed to convert chunk to base64, skipping:", { mime_type: chunk.mime_type });
            return null;
          }

          return {
            mime_type: chunk.mime_type,
            data: base64Data,
            url: chunk.data,
          };
        } catch (error) {
          Logger.error("Error processing image chunk:", { chunk, error: error instanceof Error ? error.message : String(error) });
          return null;
        }
      })
    );

    // Filter out any failed conversions
    return processedChunks.filter((chunk) => chunk !== null) as Array<{
      mime_type: string;
      data: string;
      url: string;
    }>;
  }

  // Optimizing the convertUrlToBase64 method to use caching
  private static async convertUrlToBase64(url: string): Promise<string> {
    // Checking cache first
    if (this.base64Cache.has(url)) {
      return this.base64Cache.get(url)!;
    }

    try {
      // Validate URL format first
      if (!url || typeof url !== 'string') {
        Logger.warn("Invalid URL provided to convertUrlToBase64:", { url, type: typeof url });
        return "";
      }

      // Check if it's already base64 data
      if (url.startsWith('data:')) {
        Logger.debug("URL is already a data URI, extracting base64 part");
        const base64Part = url.split(',')[1];
        if (base64Part) {
          this.base64Cache.set(url, base64Part);
          return base64Part;
        }
      }

      // Validate that it's a proper URL
      try {
        new URL(url);
      } catch (urlError) {
        Logger.warn("Invalid URL format:", { url, error: urlError });
        return "";
      }

      // Normalizing URL to prevent double encoding issues
      const normalizedUrl = decodeURIComponent(url);

      const response = await fetch(normalizedUrl);

      if (!response.ok) {
        Logger.warn("Failed to fetch URL:", { url: normalizedUrl, status: response.status, statusText: response.statusText });
        return "";
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64Data = buffer.toString("base64");

      // Cache the result
      this.base64Cache.set(url, base64Data);
      return base64Data;
    } catch (error) {
      Logger.error("Error converting URL to base64:", { url, error: error instanceof Error ? error.message : String(error) });
      return "";
    }
  }
}

// Exports
export const generateAndStoreEmbeddingsVoyage =
  VoyageEmbeddingService.generateEmbeddings;
export const processResultsWithContext = (
  results: Array<SearchResult>,
  maxTokens: number = CONFIG.SIMILARITY.MAX_TOKENS_PER_RESPONSE
): string => {
  if (!results?.length) return "No relevant content found.";
  return results.map((result) => result.content).join("\n\n");
};
