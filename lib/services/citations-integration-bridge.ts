/**
 * Citations Integration Bridge
 * 
 * Lightweight integration layer that connects inline citations with existing services
 * without modifying the original service logic. This bridge:
 * 
 * 1. Reuses Enhanced Doc Reader extracted images for OCR stage
 * 2. Preserves default RAG and Enhanced Doc Reader behavior
 * 3. Provides a clean interface for citations to access processed images
 * 4. Maintains service independence and modularity
 */

import { Logger } from "../utils/Logger";
import { DocumentMarkdownProcessor } from "./document-markdown-processor";
import { EnhancedDocReaderImageIntegration } from "./enhanced-doc-reader-integration";
import { RAGProcessor } from "./ragV1";
import { SearchService } from "./rag/SearchService";
import { SearchResult } from "./rag/types";

export interface CitationsBridgeOptions {
  preferImageReuse?: boolean;
  fallbackToExtraction?: boolean;
  enablePerformanceLogging?: boolean;
}

export interface CitationImageResult {
  success: boolean;
  images: Array<{
    mime_type: string;
    data: string;
    url: string;
    documentId: string;
    pageNumber: number;
    source: 'enhanced-doc-reader' | 'rag-processor' | 'fallback';
  }>;
  metadata: {
    totalImages: number;
    reusedFromEnhancedDocReader: number;
    extractedViaRAG: number;
    fallbackExtractions: number;
    processingTime: number;
    source: string;
  };
  error?: string;
}

/**
 * Lightweight bridge service that integrates citations with existing services
 * without modifying their core functionality
 */
export class CitationsIntegrationBridge {
  /**
   * Get images for inline citations OCR stage by reusing Enhanced Doc Reader results
   * This is the primary method for citations to access processed images
   * 
   * @param documentIds Array of document IDs to get images for
   * @param options Integration options
   * @returns Processed images ready for OCR
   */
  static async getImagesForCitationsOCR(
    documentIds: string[],
    options: CitationsBridgeOptions = {}
  ): Promise<CitationImageResult> {
    const startTime = Date.now();
    const {
      preferImageReuse = true,
      fallbackToExtraction = true,
      enablePerformanceLogging = true
    } = options;

    try {
      if (enablePerformanceLogging) {
        Logger.info("Citations bridge: Starting image retrieval for OCR", {
          documentIds,
          documentCount: documentIds.length,
          preferImageReuse,
          fallbackToExtraction,
          source: "citations-integration-bridge"
        });
      }

      let allImages: Array<{
        mime_type: string;
        data: string;
        url: string;
        documentId: string;
        pageNumber: number;
        source: 'enhanced-doc-reader' | 'rag-processor' | 'fallback';
      }> = [];

      let reusedFromEnhancedDocReader = 0;
      let extractedViaRAG = 0;
      let fallbackExtractions = 0;

      // Step 1: Try to reuse Enhanced Doc Reader images (preferred approach)
      if (preferImageReuse) {
        const storedImages = await this.getStoredEnhancedDocReaderImages(documentIds);
        
        if (storedImages.length > 0) {
          allImages.push(...storedImages.map(img => ({
            ...img,
            source: 'enhanced-doc-reader' as const
          })));
          reusedFromEnhancedDocReader = storedImages.length;

          if (enablePerformanceLogging) {
            Logger.info("Citations bridge: Successfully reused Enhanced Doc Reader images", {
              imageCount: storedImages.length,
              documentIds: [...new Set(storedImages.map(img => img.documentId))],
              source: "enhanced-doc-reader-reuse"
            });
          }
        }
      }

      // Step 2: For documents without stored images, try RAG processor (if enabled)
      const documentsWithoutImages = documentIds.filter(docId => 
        !allImages.some(img => img.documentId === docId)
      );

      if (documentsWithoutImages.length > 0 && fallbackToExtraction) {
        const ragImages = await this.getImagesViaRAGProcessor(documentsWithoutImages);
        
        if (ragImages.length > 0) {
          allImages.push(...ragImages.map(img => ({
            ...img,
            source: 'rag-processor' as const
          })));
          extractedViaRAG = ragImages.length;

          if (enablePerformanceLogging) {
            Logger.info("Citations bridge: Extracted images via RAG processor", {
              imageCount: ragImages.length,
              documentIds: documentsWithoutImages,
              source: "rag-processor-extraction"
            });
          }
        }
      }

      const processingTime = Date.now() - startTime;

      const result: CitationImageResult = {
        success: true,
        images: allImages,
        metadata: {
          totalImages: allImages.length,
          reusedFromEnhancedDocReader,
          extractedViaRAG,
          fallbackExtractions,
          processingTime,
          source: "citations-integration-bridge"
        }
      };

      if (enablePerformanceLogging) {
        Logger.info("Citations bridge: Image retrieval completed", {
          ...result.metadata,
          documentCount: documentIds.length,
          successRate: `${allImages.length}/${documentIds.length}`,
        });
      }

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      Logger.error("Citations bridge: Error retrieving images for OCR", {
        documentIds,
        error: errorMessage,
        processingTime,
        source: "citations-integration-bridge"
      });

      return {
        success: false,
        images: [],
        metadata: {
          totalImages: 0,
          reusedFromEnhancedDocReader: 0,
          extractedViaRAG: 0,
          fallbackExtractions: 0,
          processingTime,
          source: "citations-integration-bridge"
        },
        error: errorMessage
      };
    }
  }

  /**
   * Get stored images from Enhanced Doc Reader processing
   * This reuses the same images displayed in "Cites to Your Documents"
   * 
   * @param documentIds Array of document IDs
   * @returns Stored images from Enhanced Doc Reader
   */
  private static async getStoredEnhancedDocReaderImages(
    documentIds: string[]
  ): Promise<Array<{
    mime_type: string;
    data: string;
    url: string;
    documentId: string;
    pageNumber: number;
  }>> {
    try {
      // Use existing DocumentMarkdownProcessor method (no modification needed)
      const storedImages = await DocumentMarkdownProcessor.getStoredImagesForCitedDocuments(documentIds);
      
      // Transform to expected format
      return storedImages.map(img => ({
        mime_type: img.mime_type,
        data: this.extractBase64FromDataUri(img.url),
        url: img.url,
        documentId: img.documentId,
        pageNumber: img.pageNumber
      }));

    } catch (error) {
      Logger.error("Citations bridge: Error getting stored Enhanced Doc Reader images", {
        documentIds,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Get images via RAG processor for documents without stored images
   * This uses the existing RAG image processing pipeline
   * 
   * @param documentIds Array of document IDs
   * @returns Images extracted via RAG processor
   */
  private static async getImagesViaRAGProcessor(
    documentIds: string[]
  ): Promise<Array<{
    mime_type: string;
    data: string;
    url: string;
    documentId: string;
    pageNumber: number;
  }>> {
    try {
      // Use existing RAG services (no modification needed)
      const searchResults = await this.getSearchResultsForDocuments(documentIds);
      
      if (searchResults.length === 0) {
        return [];
      }

      // Extract image chunks using existing RAG processor
      const imageChunks = RAGProcessor.extractImageChunks(searchResults);
      
      if (imageChunks.length === 0) {
        return [];
      }

      // Process image chunks using existing RAG processor
      const processedImages = await RAGProcessor.processImageChunks(imageChunks);

      // Transform to expected format
      return processedImages.map((img, index) => ({
        mime_type: img.mime_type,
        data: img.data,
        url: img.url,
        documentId: this.extractDocumentIdFromSearchResults(searchResults, index),
        pageNumber: index + 1 // Estimate page number
      }));

    } catch (error) {
      Logger.error("Citations bridge: Error getting images via RAG processor", {
        documentIds,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Get search results for specific documents (helper method)
   */
  private static async getSearchResultsForDocuments(documentIds: string[]): Promise<SearchResult[]> {
    try {
      // This is a simplified approach - in practice, you might want to use a more targeted query
      const allResults: SearchResult[] = [];
      
      for (const documentId of documentIds) {
        try {
          // Use existing SearchService (no modification needed)
          const results = await SearchService.searchEmbeddings("", documentId);
          allResults.push(...results.filter(r => r.image_data));
        } catch (error) {
          Logger.warn("Citations bridge: Error searching for document", {
            documentId,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      return allResults;
    } catch (error) {
      Logger.error("Citations bridge: Error getting search results", {
        documentIds,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Extract base64 data from data URI
   */
  private static extractBase64FromDataUri(dataUri: string): string {
    if (dataUri.startsWith('data:')) {
      const base64Part = dataUri.split(',')[1];
      return base64Part || dataUri;
    }
    return dataUri;
  }

  /**
   * Extract document ID from search results (helper method)
   */
  private static extractDocumentIdFromSearchResults(searchResults: SearchResult[], index: number): string {
    if (index < searchResults.length && searchResults[index].sourceDocumentId) {
      return searchResults[index].sourceDocumentId;
    }
    return 'unknown';
  }

  /**
   * Check if Enhanced Doc Reader processing is complete for documents
   * This helps citations decide whether to wait or proceed with fallback
   * 
   * @param documentIds Array of document IDs to check
   * @returns Processing status information
   */
  static async checkEnhancedDocReaderStatus(documentIds: string[]): Promise<{
    allComplete: boolean;
    completeDocuments: string[];
    incompleteDocuments: string[];
    processingStatus: Record<string, boolean>;
  }> {
    try {
      const processingStatus: Record<string, boolean> = {};
      const completeDocuments: string[] = [];
      const incompleteDocuments: string[] = [];

      for (const documentId of documentIds) {
        try {
          // Use existing Enhanced Doc Reader integration (no modification needed)
          const status = await EnhancedDocReaderImageIntegration.checkProcessingStatus(documentId);
          const isComplete = status.isComplete && status.hasStoredImages;
          
          processingStatus[documentId] = isComplete;
          
          if (isComplete) {
            completeDocuments.push(documentId);
          } else {
            incompleteDocuments.push(documentId);
          }
        } catch (error) {
          Logger.warn("Citations bridge: Error checking document status", {
            documentId,
            error: error instanceof Error ? error.message : String(error)
          });
          processingStatus[documentId] = false;
          incompleteDocuments.push(documentId);
        }
      }

      return {
        allComplete: incompleteDocuments.length === 0,
        completeDocuments,
        incompleteDocuments,
        processingStatus
      };

    } catch (error) {
      Logger.error("Citations bridge: Error checking Enhanced Doc Reader status", {
        documentIds,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        allComplete: false,
        completeDocuments: [],
        incompleteDocuments: documentIds,
        processingStatus: Object.fromEntries(documentIds.map(id => [id, false]))
      };
    }
  }
}
