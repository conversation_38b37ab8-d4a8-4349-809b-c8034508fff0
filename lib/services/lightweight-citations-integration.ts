/**
 * Lightweight Citations Integration
 * 
 * This module initializes the lightweight integration between inline citations
 * and existing services (Enhanced Doc Reader, RAG) without modifying their
 * core implementations.
 */

import { Logger } from "../utils/Logger";
import { ServiceHookRegistry } from "./hooks/service-hook-registry";

/**
 * Integration manager for lightweight citations
 */
export class LightweightCitationsIntegration {
  private static initialized = false;
  private static initializationPromise: Promise<void> | null = null;

  /**
   * Initialize the lightweight citations integration
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      Logger.debug("Lightweight citations integration already initialized");
      return;
    }

    if (this.initializationPromise) {
      Logger.debug("Lightweight citations integration already initializing, waiting...");
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  /**
   * Perform the actual initialization
   */
  private static async performInitialization(): Promise<void> {
    try {
      Logger.info("Initializing lightweight citations integration...");

      // Step 1: Initialize the hook registry
      Logger.debug("Setting up service hook registry");
      ServiceHookRegistry.setEnabled(true);

      // Step 2: Initialize inline citations hooks
      Logger.debug("Initializing inline citations hooks");
      await import("./hooks/inline-citations-hooks");

      // Step 3: Initialize service extensions
      Logger.debug("Initializing service extensions");
      await Promise.all([
        import("./extensions/enhanced-doc-reader-extension"),
        import("./extensions/rag-extension")
      ]);

      // Step 4: Verify integration
      const stats = ServiceHookRegistry.getStats();
      Logger.info("Lightweight citations integration initialized successfully", {
        totalHooks: stats.totalHooks,
        serviceCount: stats.serviceCount,
        operationCount: stats.operationCount,
        enabled: stats.enabled
      });

      this.initialized = true;
    } catch (error) {
      Logger.error("Failed to initialize lightweight citations integration", {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    } finally {
      this.initializationPromise = null;
    }
  }

  /**
   * Check if the integration is initialized
   */
  static isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get integration status and statistics
   */
  static getStatus(): {
    initialized: boolean;
    hookStats: ReturnType<typeof ServiceHookRegistry.getStats>;
    registeredHooks: Record<string, any[]>;
  } {
    return {
      initialized: this.initialized,
      hookStats: ServiceHookRegistry.getStats(),
      registeredHooks: ServiceHookRegistry.getRegisteredHooks()
    };
  }

  /**
   * Disable the integration (for testing or troubleshooting)
   */
  static disable(): void {
    ServiceHookRegistry.setEnabled(false);
    Logger.info("Lightweight citations integration disabled");
  }

  /**
   * Enable the integration
   */
  static enable(): void {
    ServiceHookRegistry.setEnabled(true);
    Logger.info("Lightweight citations integration enabled");
  }

  /**
   * Reset the integration (clear all hooks and reinitialize)
   */
  static async reset(): Promise<void> {
    Logger.info("Resetting lightweight citations integration");
    
    ServiceHookRegistry.clearAllHooks();
    this.initialized = false;
    this.initializationPromise = null;
    
    await this.initialize();
  }
}

/**
 * Auto-initialize the integration when this module is imported
 */
LightweightCitationsIntegration.initialize().catch(error => {
  Logger.error("Auto-initialization of lightweight citations integration failed", {
    error: error instanceof Error ? error.message : String(error)
  });
});

// Export the main service for use in other modules
export { LightweightInlineCitations } from "./lightweight-inline-citations";
export { ServiceHookRegistry } from "./hooks/service-hook-registry";
export { InlineCitationsHooks } from "./hooks/inline-citations-hooks";
