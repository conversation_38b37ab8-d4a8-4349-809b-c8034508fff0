/**
 * Service Hook Registry
 * 
 * Lightweight integration system that allows services to register hooks
 * without modifying their core logic. This enables inline citations to
 * tap into Enhanced Doc Reader and RAG services.
 */

import { Logger } from "../../utils/Logger";

export interface ServiceHookContext {
  serviceId: string;
  operation: string;
  data: any;
  metadata?: Record<string, any>;
}

export interface ServiceHookResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: Record<string, any>;
}

export type ServiceHookHandler = (context: ServiceHookContext) => Promise<ServiceHookResult> | ServiceHookResult;

export interface ServiceHook {
  id: string;
  serviceId: string;
  operation: string;
  handler: ServiceHookHandler;
  priority?: number; // Lower numbers = higher priority
  enabled?: boolean;
}

/**
 * Lightweight service hook registry that allows services to register
 * hooks without modifying their core implementation
 */
export class ServiceHookRegistry {
  private static hooks: Map<string, ServiceHook[]> = new Map();
  private static enabled: boolean = true;

  /**
   * Register a hook for a specific service operation
   */
  static registerHook(hook: ServiceHook): void {
    const key = `${hook.serviceId}:${hook.operation}`;
    
    if (!this.hooks.has(key)) {
      this.hooks.set(key, []);
    }

    const hooks = this.hooks.get(key)!;
    
    // Remove existing hook with same ID if it exists
    const existingIndex = hooks.findIndex(h => h.id === hook.id);
    if (existingIndex >= 0) {
      hooks.splice(existingIndex, 1);
    }

    // Insert hook in priority order (lower priority number = higher priority)
    const priority = hook.priority ?? 100;
    const insertIndex = hooks.findIndex(h => (h.priority ?? 100) > priority);
    
    if (insertIndex >= 0) {
      hooks.splice(insertIndex, 0, hook);
    } else {
      hooks.push(hook);
    }

    Logger.debug("Service hook registered", {
      hookId: hook.id,
      serviceId: hook.serviceId,
      operation: hook.operation,
      priority,
      totalHooks: hooks.length
    });
  }

  /**
   * Unregister a hook by ID
   */
  static unregisterHook(hookId: string): boolean {
    for (const [key, hooks] of this.hooks.entries()) {
      const index = hooks.findIndex(h => h.id === hookId);
      if (index >= 0) {
        hooks.splice(index, 1);
        Logger.debug("Service hook unregistered", { hookId, key });
        return true;
      }
    }
    return false;
  }

  /**
   * Execute hooks for a specific service operation
   */
  static async executeHooks(context: ServiceHookContext): Promise<ServiceHookResult[]> {
    if (!this.enabled) {
      return [];
    }

    const key = `${context.serviceId}:${context.operation}`;
    const hooks = this.hooks.get(key) || [];
    
    if (hooks.length === 0) {
      return [];
    }

    Logger.debug("Executing service hooks", {
      serviceId: context.serviceId,
      operation: context.operation,
      hookCount: hooks.length
    });

    const results: ServiceHookResult[] = [];

    for (const hook of hooks) {
      if (hook.enabled === false) {
        continue;
      }

      try {
        const startTime = Date.now();
        const result = await hook.handler(context);
        const executionTime = Date.now() - startTime;

        Logger.debug("Service hook executed", {
          hookId: hook.id,
          success: result.success,
          executionTime,
          hasData: !!result.data
        });

        results.push(result);
      } catch (error) {
        Logger.error("Service hook execution failed", {
          hookId: hook.id,
          serviceId: context.serviceId,
          operation: context.operation,
          error: error instanceof Error ? error.message : String(error)
        });

        results.push({
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * Get all registered hooks for debugging
   */
  static getRegisteredHooks(): Record<string, ServiceHook[]> {
    const result: Record<string, ServiceHook[]> = {};
    for (const [key, hooks] of this.hooks.entries()) {
      result[key] = [...hooks]; // Return copy to prevent modification
    }
    return result;
  }

  /**
   * Enable or disable the hook system
   */
  static setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    Logger.info("Service hook registry enabled state changed", { enabled });
  }

  /**
   * Clear all hooks (useful for testing)
   */
  static clearAllHooks(): void {
    this.hooks.clear();
    Logger.debug("All service hooks cleared");
  }

  /**
   * Get hook statistics
   */
  static getStats(): {
    totalHooks: number;
    serviceCount: number;
    operationCount: number;
    enabled: boolean;
  } {
    const services = new Set<string>();
    let totalHooks = 0;

    for (const [key, hooks] of this.hooks.entries()) {
      const [serviceId] = key.split(':');
      services.add(serviceId);
      totalHooks += hooks.length;
    }

    return {
      totalHooks,
      serviceCount: services.size,
      operationCount: this.hooks.size,
      enabled: this.enabled
    };
  }
}

/**
 * Convenience function to trigger hooks from services
 */
export async function triggerServiceHook(
  serviceId: string,
  operation: string,
  data: any,
  metadata?: Record<string, any>
): Promise<ServiceHookResult[]> {
  return ServiceHookRegistry.executeHooks({
    serviceId,
    operation,
    data,
    metadata
  });
}

/**
 * Decorator for service methods to automatically trigger hooks
 */
export function withServiceHooks(serviceId: string, operation: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // Execute pre-hooks
      await triggerServiceHook(serviceId, `${operation}:before`, { args }, { method: propertyKey });

      try {
        // Execute original method
        const result = await originalMethod.apply(this, args);

        // Execute post-hooks with result
        await triggerServiceHook(serviceId, `${operation}:after`, { args, result }, { method: propertyKey });

        return result;
      } catch (error) {
        // Execute error hooks
        await triggerServiceHook(serviceId, `${operation}:error`, { args, error }, { method: propertyKey });
        throw error;
      }
    };

    return descriptor;
  };
}
