/**
 * Inline Citations Hooks
 * 
 * Lightweight hooks that integrate with Enhanced Doc Reader and RAG services
 * to reuse extracted images for inline citations OCR processing.
 */

import { Logger } from "../../utils/Logger";
import { ServiceHookRegistry, ServiceHookContext, ServiceHookResult } from "./service-hook-registry";

export interface ExtractedImageData {
  documentId: string;
  pageNumber: number;
  imageBase64: string;
  width?: number;
  height?: number;
  metadata?: Record<string, any>;
}

export interface CitationImageCache {
  documentId: string;
  images: ExtractedImageData[];
  extractedAt: Date;
  source: 'enhanced-doc-reader' | 'rag' | 'manual';
}

/**
 * Cache for storing extracted images from various services
 * for reuse in inline citations processing
 */
class CitationImageCacheManager {
  private static cache: Map<string, CitationImageCache> = new Map();
  private static maxCacheSize = 100;
  private static maxCacheAge = 30 * 60 * 1000; // 30 minutes

  static store(documentId: string, images: ExtractedImageData[], source: CitationImageCache['source']): void {
    // Clean old entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.cleanup();
    }

    this.cache.set(documentId, {
      documentId,
      images,
      extractedAt: new Date(),
      source
    });

    Logger.debug("Images cached for inline citations", {
      documentId,
      imageCount: images.length,
      source,
      cacheSize: this.cache.size
    });
  }

  static retrieve(documentId: string): CitationImageCache | null {
    const cached = this.cache.get(documentId);
    
    if (!cached) {
      return null;
    }

    // Check if cache entry is too old
    const age = Date.now() - cached.extractedAt.getTime();
    if (age > this.maxCacheAge) {
      this.cache.delete(documentId);
      Logger.debug("Expired cache entry removed", { documentId, age });
      return null;
    }

    Logger.debug("Images retrieved from cache for inline citations", {
      documentId,
      imageCount: cached.images.length,
      source: cached.source,
      age
    });

    return cached;
  }

  static cleanup(): void {
    const now = Date.now();
    const toDelete: string[] = [];

    for (const [documentId, cached] of this.cache.entries()) {
      const age = now - cached.extractedAt.getTime();
      if (age > this.maxCacheAge) {
        toDelete.push(documentId);
      }
    }

    toDelete.forEach(documentId => this.cache.delete(documentId));
    
    if (toDelete.length > 0) {
      Logger.debug("Cache cleanup completed", {
        removedEntries: toDelete.length,
        remainingEntries: this.cache.size
      });
    }
  }

  static clear(): void {
    this.cache.clear();
    Logger.debug("Citation image cache cleared");
  }

  static getStats(): {
    cacheSize: number;
    maxCacheSize: number;
    maxCacheAge: number;
    oldestEntry?: Date;
    newestEntry?: Date;
  } {
    let oldestEntry: Date | undefined;
    let newestEntry: Date | undefined;

    for (const cached of this.cache.values()) {
      if (!oldestEntry || cached.extractedAt < oldestEntry) {
        oldestEntry = cached.extractedAt;
      }
      if (!newestEntry || cached.extractedAt > newestEntry) {
        newestEntry = cached.extractedAt;
      }
    }

    return {
      cacheSize: this.cache.size,
      maxCacheSize: this.maxCacheSize,
      maxCacheAge: this.maxCacheAge,
      oldestEntry,
      newestEntry
    };
  }
}

/**
 * Hook handlers for Enhanced Doc Reader integration
 */
export class InlineCitationsHooks {
  /**
   * Initialize all inline citations hooks
   */
  static initialize(): void {
    Logger.info("Initializing inline citations hooks");

    // Hook into Enhanced Doc Reader image extraction
    ServiceHookRegistry.registerHook({
      id: 'inline-citations-enhanced-doc-reader',
      serviceId: 'enhanced-doc-reader',
      operation: 'images-extracted',
      priority: 10,
      handler: this.handleEnhancedDocReaderImages.bind(this)
    });

    // Hook into RAG image processing
    ServiceHookRegistry.registerHook({
      id: 'inline-citations-rag-images',
      serviceId: 'rag',
      operation: 'images-processed',
      priority: 10,
      handler: this.handleRagImages.bind(this)
    });

    // Hook into document processing completion
    ServiceHookRegistry.registerHook({
      id: 'inline-citations-document-processed',
      serviceId: 'document-processor',
      operation: 'document-processed',
      priority: 10,
      handler: this.handleDocumentProcessed.bind(this)
    });

    Logger.info("Inline citations hooks initialized");
  }

  /**
   * Handle images extracted by Enhanced Doc Reader
   */
  private static async handleEnhancedDocReaderImages(context: ServiceHookContext): Promise<ServiceHookResult> {
    try {
      const { documentId, pages } = context.data;

      if (!documentId || !pages || !Array.isArray(pages)) {
        return { success: false, error: "Invalid Enhanced Doc Reader data" };
      }

      // Convert Enhanced Doc Reader pages to our format
      const images: ExtractedImageData[] = pages.map((page: any) => ({
        documentId,
        pageNumber: page.pageNumber || 1,
        imageBase64: page.imageBase64,
        width: page.width,
        height: page.height,
        metadata: {
          scaleFactor: page.metadata?.scaleFactor,
          quality: page.metadata?.quality,
          renderingApproach: page.metadata?.renderingApproach
        }
      }));

      // Cache images for inline citations
      CitationImageCacheManager.store(documentId, images, 'enhanced-doc-reader');

      Logger.info("Enhanced Doc Reader images cached for inline citations", {
        documentId,
        imageCount: images.length
      });

      return {
        success: true,
        data: { documentId, imageCount: images.length },
        metadata: { source: 'enhanced-doc-reader' }
      };
    } catch (error) {
      Logger.error("Error handling Enhanced Doc Reader images", {
        error: error instanceof Error ? error.message : String(error),
        context
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Handle images processed by RAG service
   */
  private static async handleRagImages(context: ServiceHookContext): Promise<ServiceHookResult> {
    try {
      const { images, documentIds } = context.data;

      if (!images || !Array.isArray(images)) {
        return { success: false, error: "Invalid RAG image data" };
      }

      // Process each document's images
      for (const documentId of documentIds || []) {
        const documentImages = images.filter((img: any) => img.documentId === documentId);
        
        if (documentImages.length > 0) {
          const extractedImages: ExtractedImageData[] = documentImages.map((img: any) => ({
            documentId,
            pageNumber: img.pageNumber || 1,
            imageBase64: img.data || img.url,
            metadata: {
              mimeType: img.mime_type,
              source: 'rag'
            }
          }));

          CitationImageCacheManager.store(documentId, extractedImages, 'rag');
        }
      }

      Logger.info("RAG images cached for inline citations", {
        documentIds,
        totalImages: images.length
      });

      return {
        success: true,
        data: { documentIds, imageCount: images.length },
        metadata: { source: 'rag' }
      };
    } catch (error) {
      Logger.error("Error handling RAG images", {
        error: error instanceof Error ? error.message : String(error),
        context
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Handle document processing completion
   */
  private static async handleDocumentProcessed(context: ServiceHookContext): Promise<ServiceHookResult> {
    try {
      const { documentId, success } = context.data;

      if (success) {
        Logger.debug("Document processing completed, images available for citations", {
          documentId
        });
      }

      return { success: true, data: { documentId, processed: success } };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get cached images for inline citations processing
   */
  static getCachedImages(documentId: string): ExtractedImageData[] | null {
    const cached = CitationImageCacheManager.retrieve(documentId);
    return cached ? cached.images : null;
  }

  /**
   * Get cached images for multiple documents
   */
  static getCachedImagesForDocuments(documentIds: string[]): Record<string, ExtractedImageData[]> {
    const result: Record<string, ExtractedImageData[]> = {};

    for (const documentId of documentIds) {
      const images = this.getCachedImages(documentId);
      if (images) {
        result[documentId] = images;
      }
    }

    return result;
  }

  /**
   * Clear the image cache
   */
  static clearCache(): void {
    CitationImageCacheManager.clear();
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): ReturnType<typeof CitationImageCacheManager.getStats> {
    return CitationImageCacheManager.getStats();
  }

  /**
   * Cleanup expired cache entries
   */
  static cleanupCache(): void {
    CitationImageCacheManager.cleanup();
  }
}

// Auto-initialize hooks when module is imported
InlineCitationsHooks.initialize();
