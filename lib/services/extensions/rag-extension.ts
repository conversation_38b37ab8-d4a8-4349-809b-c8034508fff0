/**
 * RAG Service Extension
 * 
 * Lightweight extension that adds hooks to the RAG service
 * without modifying its core logic. This allows inline citations to tap into
 * the image processing pipeline.
 */

import { Logger } from "../../utils/Logger";
import { triggerServiceHook } from "../hooks/service-hook-registry";

/**
 * Extension wrapper for RAG Processor
 * This extends the existing service without modifying its core implementation
 */
export class RagExtension {
  /**
   * Wrap the processImageChunks method to add hooks
   */
  static wrapProcessImageChunks(originalMethod: Function) {
    return async function (this: any, imageChunks: Array<{ mime_type: string; data: string }>) {
      Logger.debug("RAG image processing started", { chunkCount: imageChunks.length });

      try {
        // Trigger before hook
        await triggerServiceHook('rag', 'image-processing-started', {
          imageChunks,
          chunkCount: imageChunks.length
        });

        // Call original method
        const result = await originalMethod.call(this, imageChunks);

        // Extract document IDs from processed images (if available)
        const documentIds = result
          .map((img: any) => img.documentId)
          .filter((id: string) => id)
          .filter((id: string, index: number, arr: string[]) => arr.indexOf(id) === index);

        // Trigger after hook with processed images
        if (result && Array.isArray(result) && result.length > 0) {
          await triggerServiceHook('rag', 'images-processed', {
            images: result,
            documentIds,
            originalChunkCount: imageChunks.length,
            processedCount: result.length
          });

          Logger.info("RAG images processed and cached", {
            originalChunks: imageChunks.length,
            processedImages: result.length,
            documentIds
          });
        }

        // Trigger completion hook
        await triggerServiceHook('rag', 'image-processing-completed', {
          success: true,
          originalChunkCount: imageChunks.length,
          processedCount: result?.length || 0,
          documentIds
        });

        return result;
      } catch (error) {
        // Trigger error hook
        await triggerServiceHook('rag', 'image-processing-error', {
          error: error instanceof Error ? error.message : String(error),
          chunkCount: imageChunks.length
        });

        Logger.error("RAG image processing failed", {
          error: error instanceof Error ? error.message : String(error),
          chunkCount: imageChunks.length
        });

        throw error;
      }
    };
  }

  /**
   * Wrap the extractImageChunks method to add hooks
   */
  static wrapExtractImageChunks(originalMethod: Function) {
    return function (this: any, chunks: any[]) {
      Logger.debug("RAG image extraction started", { chunkCount: chunks.length });

      try {
        // Trigger before hook
        triggerServiceHook('rag', 'image-extraction-started', {
          chunks,
          chunkCount: chunks.length
        });

        // Call original method
        const result = originalMethod.call(this, chunks);

        // Trigger after hook with extracted image chunks
        if (result && Array.isArray(result)) {
          triggerServiceHook('rag', 'image-chunks-extracted', {
            imageChunks: result,
            originalChunkCount: chunks.length,
            extractedCount: result.length
          });

          Logger.debug("RAG image chunks extracted", {
            originalChunks: chunks.length,
            extractedChunks: result.length
          });
        }

        return result;
      } catch (error) {
        // Trigger error hook
        triggerServiceHook('rag', 'image-extraction-error', {
          error: error instanceof Error ? error.message : String(error),
          chunkCount: chunks.length
        });

        throw error;
      }
    };
  }

  /**
   * Apply extensions to the RAG Processor class
   */
  static applyExtensions(RAGProcessor: any): void {
    if (!RAGProcessor) {
      Logger.warn("RAG Processor class not found, skipping extensions");
      return;
    }

    // Store original methods
    const originalProcessImageChunks = RAGProcessor.processImageChunks;
    const originalExtractImageChunks = RAGProcessor.extractImageChunks;

    // Apply extensions only if methods exist
    if (originalProcessImageChunks) {
      RAGProcessor.processImageChunks = this.wrapProcessImageChunks(originalProcessImageChunks);
      Logger.debug("RAG processImageChunks method extended");
    }

    if (originalExtractImageChunks) {
      RAGProcessor.extractImageChunks = this.wrapExtractImageChunks(originalExtractImageChunks);
      Logger.debug("RAG extractImageChunks method extended");
    }

    Logger.info("RAG extensions applied successfully");
  }

  /**
   * Initialize the extension by applying it to the RAG service
   */
  static async initialize(): Promise<void> {
    try {
      // Try to import the RAG processor
      let RAGProcessor;
      
      try {
        const ragModule = await import("../ragV1");
        RAGProcessor = ragModule.RAGProcessor;
      } catch (error) {
        // If ragV1 doesn't exist or doesn't export RAGProcessor, try alternative imports
        Logger.debug("Could not import from ragV1, trying alternative imports");
        
        try {
          const ragModule = await import("../rag");
          RAGProcessor = ragModule.RAGProcessor;
        } catch (altError) {
          Logger.debug("Could not import RAG processor from alternative locations");
        }
      }

      if (RAGProcessor) {
        // Apply extensions
        this.applyExtensions(RAGProcessor);
        Logger.info("RAG extension initialized successfully");
      } else {
        Logger.warn("RAG Processor not found, RAG extension not applied");
      }
    } catch (error) {
      Logger.error("Failed to initialize RAG extension", {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

/**
 * Auto-initialize the extension when module is imported
 * This ensures the hooks are set up as soon as the module is loaded
 */
RagExtension.initialize().catch(error => {
  Logger.error("Auto-initialization of RAG extension failed", {
    error: error instanceof Error ? error.message : String(error)
  });
});
