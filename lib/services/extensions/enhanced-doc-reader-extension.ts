/**
 * Enhanced Doc Reader Extension
 * 
 * Lightweight extension that adds hooks to the Enhanced Doc Reader service
 * without modifying its core logic. This allows inline citations to tap into
 * the image extraction process.
 */

import { Logger } from "../../utils/Logger";
import { triggerServiceHook } from "../hooks/service-hook-registry";

/**
 * Extension wrapper for Enhanced Doc Reader Integration
 * This extends the existing service without modifying its core implementation
 */
export class EnhancedDocReaderExtension {
  /**
   * Wrap the processPdfToImages method to add hooks
   */
  static wrapProcessPdfToImages(originalMethod: Function) {
    return async function (this: any, documentId: string, url: string, options?: any) {
      Logger.debug("Enhanced Doc Reader processing started", { documentId, url });

      try {
        // Trigger before hook
        await triggerServiceHook('enhanced-doc-reader', 'processing-started', {
          documentId,
          url,
          options
        });

        // Call original method
        const result = await originalMethod.call(this, documentId, url, options);

        // Trigger after hook with extracted images
        if (result && Array.isArray(result) && result.length > 0) {
          await triggerServiceHook('enhanced-doc-reader', 'images-extracted', {
            documentId,
            pages: result,
            url,
            options
          });

          Logger.info("Enhanced Doc Reader images extracted and cached", {
            documentId,
            pageCount: result.length
          });
        }

        // Trigger completion hook
        await triggerServiceHook('enhanced-doc-reader', 'processing-completed', {
          documentId,
          success: true,
          pageCount: result?.length || 0
        });

        return result;
      } catch (error) {
        // Trigger error hook
        await triggerServiceHook('enhanced-doc-reader', 'processing-error', {
          documentId,
          error: error instanceof Error ? error.message : String(error),
          url,
          options
        });

        Logger.error("Enhanced Doc Reader processing failed", {
          documentId,
          error: error instanceof Error ? error.message : String(error)
        });

        throw error;
      }
    };
  }

  /**
   * Wrap the waitForProcessingCompletion method to add hooks
   */
  static wrapWaitForProcessingCompletion(originalMethod: Function) {
    return async function (this: any, documentId: string, expectedPages: number, maxWaitTime: number, pollingInterval: number) {
      Logger.debug("Enhanced Doc Reader waiting for completion", { documentId, expectedPages });

      try {
        // Trigger before hook
        await triggerServiceHook('enhanced-doc-reader', 'waiting-started', {
          documentId,
          expectedPages,
          maxWaitTime,
          pollingInterval
        });

        // Call original method
        const result = await originalMethod.call(this, documentId, expectedPages, maxWaitTime, pollingInterval);

        // Trigger after hook with retrieved images
        if (result && Array.isArray(result) && result.length > 0) {
          await triggerServiceHook('enhanced-doc-reader', 'images-extracted', {
            documentId,
            pages: result,
            source: 'async-polling'
          });

          Logger.info("Enhanced Doc Reader async images retrieved and cached", {
            documentId,
            pageCount: result.length
          });
        }

        // Trigger completion hook
        await triggerServiceHook('enhanced-doc-reader', 'waiting-completed', {
          documentId,
          success: true,
          pageCount: result?.length || 0
        });

        return result;
      } catch (error) {
        // Trigger error hook
        await triggerServiceHook('enhanced-doc-reader', 'waiting-error', {
          documentId,
          error: error instanceof Error ? error.message : String(error),
          expectedPages
        });

        throw error;
      }
    };
  }

  /**
   * Apply extensions to the Enhanced Doc Reader Integration class
   */
  static applyExtensions(EnhancedDocReaderIntegration: any): void {
    if (!EnhancedDocReaderIntegration) {
      Logger.warn("Enhanced Doc Reader Integration class not found, skipping extensions");
      return;
    }

    // Store original methods
    const originalProcessPdfToImages = EnhancedDocReaderIntegration.processPdfToImages;
    const originalWaitForProcessingCompletion = EnhancedDocReaderIntegration.waitForProcessingCompletion;

    // Apply extensions only if methods exist
    if (originalProcessPdfToImages) {
      EnhancedDocReaderIntegration.processPdfToImages = this.wrapProcessPdfToImages(originalProcessPdfToImages);
      Logger.debug("Enhanced Doc Reader processPdfToImages method extended");
    }

    if (originalWaitForProcessingCompletion) {
      EnhancedDocReaderIntegration.waitForProcessingCompletion = this.wrapWaitForProcessingCompletion(originalWaitForProcessingCompletion);
      Logger.debug("Enhanced Doc Reader waitForProcessingCompletion method extended");
    }

    Logger.info("Enhanced Doc Reader extensions applied successfully");
  }

  /**
   * Initialize the extension by applying it to the Enhanced Doc Reader service
   */
  static async initialize(): Promise<void> {
    try {
      // Dynamically import the Enhanced Doc Reader Integration
      const { EnhancedDocReaderImageIntegration } = await import("../enhanced-doc-reader-integration");
      
      // Apply extensions
      this.applyExtensions(EnhancedDocReaderImageIntegration);
      
      Logger.info("Enhanced Doc Reader extension initialized successfully");
    } catch (error) {
      Logger.error("Failed to initialize Enhanced Doc Reader extension", {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

/**
 * Auto-initialize the extension when module is imported
 * This ensures the hooks are set up as soon as the module is loaded
 */
EnhancedDocReaderExtension.initialize().catch(error => {
  Logger.error("Auto-initialization of Enhanced Doc Reader extension failed", {
    error: error instanceof Error ? error.message : String(error)
  });
});
