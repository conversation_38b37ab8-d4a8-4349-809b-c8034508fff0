# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

.env
.vercel
.vscode
.env*.local
.env.sentry-build-plugin
/docs
.history
.zip

#vscode
.vscode/

/.idea/.gitignore
/.idea/AugmentWebviewStateStore.xml
/CITATION_CLEANUP_PLAN.md
/CITATION_CLEANUP_SUMMARY.md
/.idea/iqidis.iml
/LEGACY_CITATION_PIPELINE_SUMMARY.md
/LEGACY_PIPELINE_LOGIC_REVIEW.md
/.idea/modules.xml
/.idea/inspectionProfiles/Project_Default.xml
/RELEVANCE_SCORING_IMPLEMENTATION.md
/tsconfig.tsbuildinfo
/.idea/vcs.xml
/hooks/docs/
/debug-images/
/debug-markdown/

