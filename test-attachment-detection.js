// Test the attachment detection logic I implemented

console.log("Testing attachment detection logic...");

// Test case 1: Messages with attachments
const messagesWithAttachments = [
  { 
    content: "Hello", 
    experimental_attachments: [{ name: 'test.pdf', contentType: 'application/pdf' }] 
  },
  { 
    content: "How are you?", 
    experimental_attachments: [] 
  }
];

// Test case 2: Messages without attachments
const messagesWithoutAttachments = [
  { content: "Hello" },
  { content: "How are you?", experimental_attachments: [] }
];

// Test case 3: Undefined/null messages
const undefinedMessages = undefined;
const nullMessages = null;

// The logic I implemented
function hasAttachedDocuments(messages) {
  return messages?.some(msg => 
    msg.experimental_attachments && msg.experimental_attachments.length > 0
  ) || false;
}

// Run tests
console.log("\n=== Test Results ===");
console.log("1. Messages with attachments:", hasAttachedDocuments(messagesWithAttachments));
console.log("2. Messages without attachments:", hasAttachedDocuments(messagesWithoutAttachments));
console.log("3. Undefined messages:", hasAttachedDocuments(undefinedMessages));
console.log("4. Null messages:", hasAttachedDocuments(nullMessages));

// Expected results
console.log("\n=== Expected Results ===");
console.log("1. Should be true (has attachments)");
console.log("2. Should be false (no attachments)");
console.log("3. Should be false (undefined messages)");
console.log("4. Should be false (null messages)");

console.log("\n✅ Logic test completed successfully!");
