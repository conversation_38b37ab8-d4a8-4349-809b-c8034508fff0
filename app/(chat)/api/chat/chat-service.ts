import { convertToCoreMessages } from "ai";
import { NextRequest } from "next/server";
import { LRUCache } from "lru-cache";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import { DEFAULT_CHAT_VISIBILITY } from "@/lib/types";
import { User as NextUser } from "next-auth";
import { logServerEvent } from "@/lib/analytics/events-server";
import { ServerErrorEvent, ServerSuccessEvent } from "@/lib/analytics/event-types";
import { serializeError } from "@/lib/utils/errorUtils";

import {
  getChatById,
  saveChat,
  saveMessages,
  updateChatTitleById,
  deleteChatById,
  updateChatTimestamp,
  getUserPreferences,
} from "@/lib/db/queries";

import { db } from "@/lib/db/db";
import { message } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

import { checkUsageLimits } from "@/lib/db/chatUsage";

import { generateTitleFromUserMessage } from "../../actions";

import {
  convertMessagesForLLMs,
  countTokens,
  evaluatePromptComparison,
} from "@/lib/services/llmService";

import { InlineCitationsGenerator } from "@/lib/services/inline-citations-generator";
import { InlineCitationsTrigger } from "@/lib/services/inline-citations-trigger";

import {
  generateUUID,
  getMostRecentUserMessage,
  getDocumentIdsFromLastUserMessage,
  fixMessageAttachments,
} from "@/lib/utils";

import { documentProcessor } from "./document-processor";
import { measureExecutionTime } from "@/lib/utils/performance";
import { RAGProcessor } from "@/lib/services/ragV1";
import { SearchService } from "@/lib/services/rag/SearchService";
import { QueryRewriteService } from "@/lib/services/rag/QueryRewriteService";
import { PerplexityService } from "@/lib/services/rag/PerplexityService";
import { ChainOfThoughtService } from "@/lib/services/chainOfThoughtService";
import { Model } from "@/lib/ai/models";
import type { Message } from "@/lib/db/schema";

/* -----------------------------------------------------------------------------
 * Types
 * ---------------------------------------------------------------------------*/

export type ChatRequestData = {
  includeInternet?: boolean;
  ragEnabled?: boolean;
  useLegacyModel?: boolean;
  [key: string]: any;
};

export type ChatRequest = {
  id: string;
  messages: any[];
  modelId: string;
  data?: ChatRequestData;
  useLegacyModel?: boolean;
  [key: string]: any;
};

export type ChatMessagingOptions = {
  chatId: string;
  messages: any[];
  userId: string;
  model: Model;
  provider: any;
  includeInternet: boolean;
  ragEnabled: boolean;
  session: { user: NextUser };
};

// Adding a preferences cache with a reasonable TTL
const preferencesCache = new LRUCache<string, any>({
  max: 100,
  ttl: 1000 * 60 * 15, // Cache preferences for 15 minutes
});

/* -----------------------------------------------------------------------------
 * Main Functions
 * ---------------------------------------------------------------------------*/

/**
 * Main handler for chat messaging
 */
export async function handleChatMessaging({
  chatId,
  messages,
  userId,
  model,
  provider,
  includeInternet = false,
  ragEnabled = false,
  session,
  data, // Add data parameter
}: {
  chatId: string;
  messages: any[];
  userId: string;
  model: Model;
  provider: any;
  includeInternet?: boolean;
  ragEnabled?: boolean;
  session: { user: NextUser };
  data?: ChatRequestData; // Add data type
}): Promise<Response> {
  // Get chat first
  const chat = await measureExecutionTime(getChatById, "getChatById", {
    id: chatId,
  });

  // Check usage limits
  const usageLimitResult = await measureExecutionTime(
    checkUsageLimits,
    "checkUsageLimits",
    session.user,
    chat,
    chatId
  );

  // If usage is restricted, return error response
  if (usageLimitResult.restricted) {
    logServerEvent(ServerSuccessEvent.CHAT_REQUEST_SERVED, {
      chatId,
      userId,
      userEmail : session.user.email,
      modelId: model.id,
      // includeInternet,
      // ragEnabled,
      // useLegacyModel:data?.useLegacyModel ?? false,
      messageId:data?.messageId,
      responseType: "USAGE_LIMIT_EXCEEDED",
    });
    return new Response(
      JSON.stringify({
        error: {
          type: "USAGE_LIMIT_EXCEEDED",
          uiMessage: usageLimitResult.errorMessage,
        },
      }),
      {
        status: 429,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  // Process message attachments
  const fixedMessages = fixMessageAttachments(messages);

  // Convert messages to core messages for processing
  const coreMessages = convertToCoreMessages(fixedMessages);
  const userMessage = getMostRecentUserMessage(coreMessages);

  // Extract command text from user message
  let commandText = "";
  if (typeof userMessage?.content === "string") {
    commandText = userMessage.content.trim();
  } else if (Array.isArray(userMessage?.content)) {
    commandText = userMessage.content
      .filter((c: any) => c.type === "text")
      .map((c: any) => c.text)
      .join(" ")
      .trim();
  }

  if (!chat) {
    const title = await measureExecutionTime(
      generateTitleFromUserMessage,
      "generateTitleFromUserMessage",
      { message: userMessage }
    );

    await measureExecutionTime(saveChat, "saveChat", {
      id: chatId,
      userId,
      title,
      modelId: model.id,
      visibility: DEFAULT_CHAT_VISIBILITY,
    });
  } else {
    // Update the timestamp for existing chat - only once per request
    Logger.info("Updating timestamp for existing chat", { id: chatId });
    await updateChatTimestamp({ id: chatId });
  }

  // Process file attachments and extract text
  const { filesText, filesTextToInclude } =
    await documentProcessor.processDocuments(
      fixedMessages,
      userId,
      chatId,
      ragEnabled
    );

  // Process messages without file attachments
  const messagesWithoutFiles =
    await documentProcessor.prepareMessagesWithoutFiles(fixedMessages);
  const coreMessagesWithoutFiles = convertToCoreMessages(messagesWithoutFiles);

  // Generate IDs for messages or use client-provided ID if available
  const userMessageId = data?.messageId || generateUUID();
  const finalMessageId = generateUUID();

  Logger.info("Using message IDs:", {
    userMessageId,
    finalMessageId,
    isClientProvided: !!data?.messageId,
  });

  // Save user message
  await measureExecutionTime(saveMessages, "saveMessages", {
    messages: [
      {
        ...userMessage,
        id: userMessageId,
        createdAt: new Date(),
        chatId,
        experimental_attachments: userMessage?.experimental_attachments ?? [],
      },
    ],
  });

  // Convert messages for LLM processing
  const llmMessages = await convertMessagesForLLMs(coreMessagesWithoutFiles);

  // Save entry to 'messageDocument' table for document persistence
  // Only do this once per request
  const documentIds = getDocumentIdsFromLastUserMessage(fixedMessages);
  if (documentIds.length > 0) {
    // Process document associations in the background to avoid blocking the response
    (async () => {
      try {
        await documentProcessor.saveMessageDocuments(
          userMessageId,
          documentIds
        );
        Logger.info("Successfully associated documents with message", {
          messageId: userMessageId,
          documentCount: documentIds.length,
        });
      } catch (error) {
        Logger.error("Failed to associate documents with message", {
          error,
          messageId: userMessageId,
          documentCount: documentIds.length,
        });
        // Server-side code can't use localStorage, so we'll log the error
        // and implement a server-side retry mechanism

        // Store pending associations in a database table or use a queue system
        // For now, we'll just log the error and continue
      }
    })();
  }

  const hasFiles = fixedMessages.some(
    (msg) =>
      msg.experimental_attachments && msg.experimental_attachments.length > 0
  );
  Logger.debug("Has files:", hasFiles);

  // Handle RAG and image processing
  let processedImages: Array<{
    mime_type: string;
    data: string;
    url: string;
  }> = [];

  if (ragEnabled) {
    try {
      Logger.debug("Include internet:", includeInternet);
      Logger.debug("RAG enabled search:", ragEnabled);
      const relevantChunks = await SearchService.searchEmbeddings(
        commandText,
        chatId
      );
      Logger.debug("Found relevant chunks:", relevantChunks.length);

      const imageChunks =
        relevantChunks.length > 0
          ? RAGProcessor.extractImageChunks(relevantChunks)
          : [];
      Logger.debug("Found image chunks:", imageChunks.length);

      processedImages =
        imageChunks.length > 0
          ? await RAGProcessor.processImageChunks(imageChunks)
          : [];
      Logger.debug("Processed images:", processedImages.length);
    } catch (error) {
      Logger.error("Error in RAG pipeline", error);
      logServerEvent(
        ServerErrorEvent.CHAT_ROUTE_ERROR,
        {
          chatId,
          userId,
          errorMessage: "Error in RAG pipeline",
          error: serializeError(error),
        },
        true
      );
      return new Response("Error processing your query", { status: 500 });
    }
  }

  // Internet search integration
  let perplexityAnswer: {
    mainContent: string;
    citations: string[];
    formattedContent: string;
  } = {
    mainContent: "",
    citations: [],
    formattedContent: "",
  };

  let rewrittenQuery = "";
  let chainOfThoughtsPromise: Promise<string> | null = null;

  if (includeInternet) {
    const chatHistory = llmMessages
      .map((msg) => `${msg.role}: ${msg.content}`)
      .join("\n");

    rewrittenQuery = await QueryRewriteService.rewriteQuery(
      commandText,
      chatHistory,
      processedImages,
      filesTextToInclude
    );
    perplexityAnswer = await PerplexityService.getAnswer(rewrittenQuery);
  } else {
    // Start chain of thoughts generation in parallel
    chainOfThoughtsPromise = measureExecutionTime(
      ChainOfThoughtService.generateChainOfThought,
      "generateChainOfThought",
      commandText
    );
  }

  // Build system prompt
  let preferences;
  const cachedPreferences = preferencesCache.get(userId);

  if (cachedPreferences) {
    preferences = cachedPreferences;
    Logger.debug("Using cached user preferences", { userId });
  } else {
    preferences = await measureExecutionTime(
      getUserPreferences,
      "getUserPreferences",
      userId
    );
    // Cache the preferences for future requests
    preferencesCache.set(userId, preferences);
  }

  // Get provider's system prompt
  const baseSystemPrompt = provider.getSystemPrompt
    ? provider.getSystemPrompt(model, preferences)
    : "";

  // Stream response from provider
  return provider.streamResponse(
    llmMessages,
    commandText,
    baseSystemPrompt,
    userMessageId,
    finalMessageId,
    chatId,
    rewrittenQuery,
    chainOfThoughtsPromise,
    perplexityAnswer,
    processedImages,
    filesTextToInclude,
    userId,
    session.user.email,
    userId
      ? async (
          msgId: string,
          chatId: string,
          content: string,
          metadata: any
        ) => {
          // If we have a chain of thoughts promise, resolve it here
          const resolvedChainOfThoughts = chainOfThoughtsPromise
            ? await chainOfThoughtsPromise
            : undefined;

          await saveMessageWithErrorHandling(
            msgId,
            chatId,
            content,
            userId,
            resolvedChainOfThoughts,
            metadata,
            fixedMessages, // Pass the messages for trigger evaluation
            !!chat, // Pass whether chat exists
            false, // isReadonly is false for normal chat flow
            ragEnabled // Pass ragEnabled flag
          );
        }
      : undefined
  );
}

/**
 * PATCH Handler for updating chat title
 */
export async function updateChatTitle(request: NextRequest): Promise<Response> {
  const { id, title } = await request.json();
  if (!id || !title) {
    return new Response("Missing required fields", { status: 400 });
  }

  const session = await auth();
  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const chat = await getChatById({ id });
    if (chat.userId !== session.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }
    await updateChatTitleById({ id, title });
    return new Response("Chat renamed", { status: 200 });
  } catch (error) {
    console.error(`Error renaming chat ${id}:`, error);
    return new Response("An error occurred while renaming chat", {
      status: 500,
    });
  }
}

/**
 * DELETE Handler for chat deletion
 */
export async function deleteChat(request: NextRequest): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");
  if (!id) {
    return new Response("Not Found", { status: 404 });
  }

  const session = await auth();
  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const chat = await getChatById({ id });
    if (chat.userId !== session.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }
    await deleteChatById({ id });
    return new Response("Chat deleted", { status: 200 });
  } catch (error) {
    Logger.error(`Error deleting chat ${id}:`, error);
    logServerEvent(
      ServerErrorEvent.CHAT_DELETE_ERROR,
      {
        chatId: id,
        userId: session.user.id,
        errorMessage: "Failed to delete chat",
        error: serializeError(error),
      },
      true
    );
    return new Response("An error occurred while processing your request", {
      status: 500,
    });
  }
}

/**
 * Save message handler with error logging and conditional inline citations generation
 */
export async function saveMessageWithErrorHandling(
  finalMessageId: string,
  chatId: string,
  content: string,
  userId: string,
  chainOfThoughts?: string,
  metadata?: any,
  messages?: any[],
  chatExists?: boolean,
  isReadonly?: boolean,
  ragEnabled?: boolean
): Promise<void> {
  try {
    Logger.info("Saving message and triggering async citation processing", {
      messageId: finalMessageId,
      chatId,
      contentLength: content.length,
    });

    // Process citations synchronously before saving the message
    let finalContent = content;
    let citationMetadata: any = {
      triggered: false,
      enabled: false,
      citationCount: 0,
    };

    try {
      Logger.info("Starting synchronous citation processing", {
        messageId: finalMessageId,
        chatId,
        contentLength: content.length,
      });

      // Check if citations should be triggered
      const triggerResult = await InlineCitationsTrigger.shouldTriggerInlineCitations(
        finalMessageId,
        chatId,
        messages || [],
        chatExists || false,
        isReadonly || false
      );

      // Skip automatic citation generation if RAG is enabled (documents attached)
      // This prevents Enhanced Doc Reader async processing errors
      // Citations will be generated on-demand via "Generate Citations" button
      if ((ragEnabled ?? false) && triggerResult.shouldTrigger) {
        Logger.info("RAG mode enabled - deferring citation generation to on-demand button click", {
          messageId: finalMessageId,
          reason: "avoid-enhanced-doc-reader-async-processing-errors",
          triggerReason: triggerResult.reason,
          integrationMode: "lightweight-hooks"
        });

        // Initialize lightweight citations integration to set up hooks
        try {
          const { LightweightCitationsIntegration } = await import("@/lib/services/lightweight-citations-integration");
          await LightweightCitationsIntegration.initialize();
          Logger.debug("Lightweight citations integration initialized for RAG mode", {
            messageId: finalMessageId
          });
        } catch (error) {
          Logger.warn("Failed to initialize lightweight citations integration", {
            messageId: finalMessageId,
            error: error instanceof Error ? error.message : String(error)
          });
        }

        // Store metadata indicating citations are available but deferred
        await db
          .update(message)
          .set({
            metadata: {
              ...(metadata || {}),
              inlineCitations: {
                enabled: false,
                triggered: false,
                deferred: true,
                deferredReason: "rag-mode-enabled",
                availableForGeneration: true,
                triggerConditionsMet: true,
                triggerReason: triggerResult.reason,
              }
            }
          })
          .where(eq(message.id, finalMessageId));

        Logger.info("Citation generation deferred - marked as available for on-demand generation", {
          messageId: finalMessageId,
        });

        // Set citation metadata to indicate deferred state
        citationMetadata = {
          triggered: false,
          triggerReason: "deferred-due-to-rag-mode",
          triggerConditions: triggerResult.conditions,
          enabled: false,
          citationCount: 0,
          deferred: true,
          deferredReason: "rag-mode-enabled",
          availableForGeneration: true,
        };
      } else if (triggerResult.shouldTrigger) {
        Logger.info("Citation conditions met, generating citations", {
          messageId: finalMessageId,
          reason: triggerResult.reason,
        });

        // Generate citations synchronously (non-RAG mode)
        const citationResult = await InlineCitationsGenerator.generateInlineCitations(
          finalMessageId,
          chatId,
          content
        );

        if (citationResult.success && citationResult.citationCount > 0) {
          finalContent = citationResult.citedContent;
          citationMetadata = {
            triggered: true,
            triggerReason: triggerResult.reason,
            triggerConditions: triggerResult.conditions,
            enabled: true,
            citationCount: citationResult.citationCount,
            citations: citationResult.citations,
            insertionLocations: citationResult.insertionLocations,
            originalContentLength: citationResult.originalContent.length,
            citedContentLength: citationResult.citedContent.length,
          };

          Logger.info("Citations generated successfully", {
            messageId: finalMessageId,
            citationCount: citationResult.citationCount,
            contentLengthChange: citationResult.citedContent.length - content.length,
          });
        } else {
          citationMetadata = {
            triggered: true,
            triggerReason: triggerResult.reason,
            triggerConditions: triggerResult.conditions,
            enabled: false,
            citationCount: 0,
          };
          Logger.info("No citations generated", { messageId: finalMessageId });
        }
      } else {
        citationMetadata = {
          triggered: false,
          triggerReason: triggerResult.reason,
          triggerConditions: triggerResult.conditions,
          enabled: false,
          citationCount: 0,
        };
        Logger.info("Citation conditions not met", {
          messageId: finalMessageId,
          reason: triggerResult.reason,
        });
      }
    } catch (error) {
      Logger.error("Error in synchronous citation processing", {
        messageId: finalMessageId,
        error,
      });
      citationMetadata = {
        triggered: false,
        triggerReason: "Error during processing",
        enabled: false,
        citationCount: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }

    // Prepare final metadata with citation results
    const finalMetadata = {
      ...(metadata || {}),
      internetResults: metadata?.internetResults,
      relevantImages: metadata?.relevantImages,
      rewrittenQuery: metadata?.rewrittenQuery,
      chainOfThoughts: chainOfThoughts,
      inlineCitations: citationMetadata,
    } as Message["metadata"];

    // Save message with final content and metadata
    await saveMessages({
      messages: [
        {
          id: finalMessageId,
          chatId,
          role: "assistant",
          content: finalContent, // Save final content with citations
          createdAt: new Date(),
          metadata: finalMetadata,
        },
      ],
    });

    Logger.info("Message saved successfully with citations", {
      messageId: finalMessageId,
      chatId,
      hasCitations: citationMetadata.citationCount > 0,
      citationCount: citationMetadata.citationCount,
    });

  } catch (error) {
    logServerEvent(ServerErrorEvent.CHAT_ROUTE_ERROR, {
      chatId,
      userId,
      errorMessage: "Failed to save chat",
      error: serializeError(error),
    });
    Logger.error("Failed to save chat:", error);
  }
}
