import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import { InlineCitationsGenerator } from "@/lib/services/inline-citations-generator";
import { InlineCitationsTrigger } from "@/lib/services/inline-citations-trigger";
import { DocumentMarkdownProcessor } from "@/lib/services/document-markdown-processor";
import { CitationPerformanceMonitor } from "@/lib/services/citation-performance-monitor";
import { EnhancedDocReaderImageIntegration } from "@/lib/services/enhanced-doc-reader-integration";

import { db } from "@/lib/db";
import { message, chat, messageDocuments, sourceDocuments } from "@/lib/db/schema";
import { eq, and, sql } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const maxDuration = 60; // 1 minute timeout

interface GenerateCitationsRequest {
  messageId: string;
  chatId: string;
  forceRegenerate?: boolean;
}

interface DocumentProcessingStatus {
  documentId: string;
  filename: string;
  isProcessed: boolean;
  hasMarkdown: boolean;
  hasChunks: boolean;
  processingError?: string;
}

interface CitationGenerationResponse {
  success: boolean;
  messageId: string;
  citationCount: number;
  citedContent?: string;
  citations?: any[];
  relevantImages?: Array<{ mime_type: string; url: string }>; // Add images for "Cites to your documents"
  documentStatus: DocumentProcessingStatus[];
  processingTime: number;
  performanceReport?: string;
  performanceRecommendations?: any[];
  error?: string;
  warnings?: string[];
}

/**
 * POST /api/citations/generate
 * Generates inline citations for a specific message on-demand
 */
export async function POST(request: NextRequest): Promise<NextResponse<CitationGenerationResponse>> {
  const startTime = Date.now();
  let messageId = "";
  let chatId = "";

  // Initialize performance monitoring
  CitationPerformanceMonitor.resetMetrics();
  CitationPerformanceMonitor.startTimer("total-pipeline");
  CitationPerformanceMonitor.recordMemoryUsage();

  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          messageId: "",
          citationCount: 0,
          documentStatus: [],
          processingTime: Date.now() - startTime,
          error: "Unauthorized"
        },
        { status: 401 }
      );
    }

    // Parse request body
    const body: GenerateCitationsRequest = await request.json();
    ({ messageId, chatId } = body);
    const { forceRegenerate = false } = body;

    Logger.info("Received citation generation request", {
      requestBody: {
        messageId: messageId || "undefined",
        chatId: chatId || "undefined",
        forceRegenerate,
        bodyKeys: Object.keys(body)
      },
      userId: session.user.id
    });

    if (!messageId || !chatId) {
      Logger.error("Missing required parameters for citation generation", {
        messageId: messageId || "undefined",
        chatId: chatId || "undefined",
        requestBody: body,
        userId: session.user.id
      });

      return NextResponse.json(
        {
          success: false,
          messageId: messageId || "",
          citationCount: 0,
          documentStatus: [],
          processingTime: Date.now() - startTime,
          error: "Missing messageId or chatId"
        },
        { status: 400 }
      );
    }

    Logger.info("Starting on-demand citation generation", {
      messageId,
      chatId,
      userId: session.user.id,
      forceRegenerate
    });

    // Step 1: Get message content and verify ownership
    const messageRecord = await db
      .select({
        id: message.id,
        content: message.content,
        chatId: message.chatId,
        userId: chat.userId,
        metadata: message.metadata
      })
      .from(message)
      .innerJoin(chat, eq(message.chatId, chat.id))
      .where(and(
        eq(message.id, messageId),
        eq(chat.userId, session.user.id)
      ))
      .limit(1);

    if (messageRecord.length === 0) {
      return NextResponse.json(
        {
          success: false,
          messageId,
          citationCount: 0,
          documentStatus: [],
          processingTime: Date.now() - startTime,
          error: "Message not found or access denied"
        },
        { status: 404 }
      );
    }

    const messageData = messageRecord[0];
    // Convert JSON content to string - the content field is stored as JSON in the database
    const content = typeof messageData.content === 'string'
      ? messageData.content
      : JSON.stringify(messageData.content);

    if (!content || content.trim() === '' || content === '{}' || content === 'null') {
      return NextResponse.json(
        {
          success: false,
          messageId,
          citationCount: 0,
          documentStatus: [],
          processingTime: Date.now() - startTime,
          error: "Message has no content"
        },
        { status: 400 }
      );
    }

    // Step 2: Check if citations already exist and not forcing regeneration
    const existingMetadata = messageData.metadata as any;
    if (!forceRegenerate && existingMetadata?.inlineCitations?.citationCount > 0) {
      Logger.info("Citations already exist for message", {
        messageId,
        existingCount: existingMetadata.inlineCitations.citationCount
      });

      return NextResponse.json({
        success: true,
        messageId,
        citationCount: existingMetadata.inlineCitations.citationCount,
        citedContent: existingMetadata.inlineCitations.citedContent || content,
        citations: existingMetadata.inlineCitations.citations || [],
        documentStatus: [],
        processingTime: Date.now() - startTime,
        warnings: ["Citations already exist. Use forceRegenerate=true to regenerate."]
      });
    }

    // Step 3: Check document processing status
    const documentStatus = await checkDocumentProcessingStatus(messageId);
    
    // Step 4: Ensure all documents are processed
    const unprocessedDocs = documentStatus.filter(doc => !doc.isProcessed);
    if (unprocessedDocs.length > 0) {
      Logger.warn("Some documents are not fully processed", {
        messageId,
        unprocessedCount: unprocessedDocs.length,
        unprocessedDocs: unprocessedDocs.map(d => ({ id: d.documentId, filename: d.filename }))
      });

      // Attempt to process unprocessed documents
      await processUnprocessedDocuments(unprocessedDocs);
      
      // Re-check status after processing attempt
      const updatedStatus = await checkDocumentProcessingStatus(messageId);
      const stillUnprocessed = updatedStatus.filter(doc => !doc.isProcessed);
      
      if (stillUnprocessed.length > 0) {
        return NextResponse.json({
          success: false,
          messageId,
          citationCount: 0,
          documentStatus: updatedStatus,
          processingTime: Date.now() - startTime,
          error: `${stillUnprocessed.length} documents are not ready for citation processing`,
          warnings: stillUnprocessed.map(d => `Document ${d.filename} is not processed`)
        });
      }
    }

    // Step 5: Verify trigger conditions
    const triggerResult = await InlineCitationsTrigger.shouldTriggerInlineCitations(
      messageId,
      chatId,
      [], // We'll pass empty array since we're forcing generation
      true, // chatExists
      false // isReadonly
    );

    if (!triggerResult.shouldTrigger && !forceRegenerate) {
      return NextResponse.json({
        success: false,
        messageId,
        citationCount: 0,
        documentStatus,
        processingTime: Date.now() - startTime,
        error: `Citation conditions not met: ${triggerResult.reason}`,
        warnings: ["Use forceRegenerate=true to bypass trigger conditions"]
      });
    }

    // Step 6: Generate citations using Enhanced Doc Reader images from "View Cites to Your Documents"
    Logger.info("Generating citations for message using Enhanced Doc Reader images", { messageId, chatId });

    CitationPerformanceMonitor.startTimer("citation-generation");

    // Use the lightweight integration: reuse cached images from Enhanced Doc Reader and RAG
    // This leverages the hook system to access images without modifying core services
    Logger.info("Using lightweight citations integration with cached images", {
      messageId,
      chatId,
      source: "lightweight-hooks-integration",
      method: "manual-generation-only"
    });

    // Initialize lightweight integration if not already done
    const { LightweightInlineCitations, LightweightCitationsIntegration } = await import("@/lib/services/lightweight-citations-integration");
    await LightweightCitationsIntegration.initialize();

    // Try lightweight service first (uses cached images from hooks)
    const lightweightResult = await LightweightInlineCitations.generateInlineCitations(
      messageId,
      chatId,
      content,
      {
        maxCitations: 10,
        minConfidence: 0.7,
        useCache: true,
        fallbackToExtraction: false // Don't fallback automatically
      }
    );

    let citationResult;

    // If lightweight service found cached images, use its result
    if (lightweightResult.success && lightweightResult.imagesSources.length > 0) {
      Logger.info("Lightweight citations completed using cached images", {
        messageId,
        citationCount: lightweightResult.citationCount,
        imagesSources: lightweightResult.imagesSources
      });

      citationResult = {
        success: lightweightResult.success,
        originalContent: lightweightResult.originalContent,
        citedContent: lightweightResult.citedContent,
        citationCount: lightweightResult.citationCount,
        citations: lightweightResult.citations,
        insertionLocations: lightweightResult.insertionLocations,
        error: lightweightResult.error
      };
    } else {
      // Fallback to original service if no cached images available
      Logger.info("No cached images available, falling back to original service", {
        messageId,
        lightweightError: lightweightResult.error
      });

      citationResult = await InlineCitationsGenerator.generateInlineCitationsWithStoredImages(
        messageId,
        chatId,
        content
      );
    }
    CitationPerformanceMonitor.endTimer("citation-generation", "Citation Generation", "citationGeneration");

    if (!citationResult.success) {
      return NextResponse.json({
        success: false,
        messageId,
        citationCount: 0,
        documentStatus,
        processingTime: Date.now() - startTime,
        error: citationResult.error || "Citation generation failed"
      });
    }

    // Step 6.5: Extract images for "Cites to your documents" functionality
    // Only extract images from documents that were actually cited
    Logger.info("Extracting images for Cites to your documents", {
      messageId,
      chatId,
      citationCount: citationResult.citationCount,
      extractOnlyCitedDocs: true
    });

    CitationPerformanceMonitor.startTimer("image-extraction");
    let relevantImages: Array<{ mime_type: string; url: string }> = [];

    if (citationResult.citations && citationResult.citations.length > 0) {
      // Use optimized flow: retrieve stored images from document processing
      relevantImages = await getStoredImagesFromCitedDocuments(messageId, citationResult.citations);
    } else {
      // Fallback: if no citations, get stored images from first few associated documents
      Logger.info("No citations found, using fallback stored image retrieval", { messageId });
      relevantImages = await getStoredImagesFromAssociatedDocuments(messageId, 2); // Limit to 2 docs
    }

    CitationPerformanceMonitor.endTimer("image-extraction", "Image Extraction", "documentProcessing");

    // Step 7: Update message with citation data
    const updatedMetadata = {
      ...existingMetadata,
      inlineCitations: {
        enabled: true,
        triggered: true,
        citationCount: citationResult.citationCount,
        citations: citationResult.citations,
        citedContent: citationResult.citedContent,
        generatedAt: new Date().toISOString(),
        onDemand: true
      },
      relevantImages: relevantImages // Add images for "Cites to your documents" button
    };

    await db
      .update(message)
      .set({ 
        metadata: updatedMetadata,
        content: citationResult.citedContent // Update content with citations
      })
      .where(eq(message.id, messageId));

    // Finalize performance monitoring
    CitationPerformanceMonitor.endTimer("total-pipeline", "Total Pipeline", "citationGeneration");
    CitationPerformanceMonitor.recordMemoryUsage();

    const performanceReport = CitationPerformanceMonitor.generatePerformanceReport();
    const performanceRecommendations = CitationPerformanceMonitor.analyzePerformance();

    CitationPerformanceMonitor.logPerformanceSummary(messageId);

    Logger.info("On-demand citation generation completed", {
      messageId,
      citationCount: citationResult.citationCount,
      processingTime: Date.now() - startTime,
      recommendationCount: performanceRecommendations.length
    });

    return NextResponse.json({
      success: true,
      messageId,
      citationCount: citationResult.citationCount,
      citedContent: citationResult.citedContent,
      citations: citationResult.citations,
      relevantImages: relevantImages,
      documentStatus,
      processingTime: Date.now() - startTime,
      performanceReport,
      performanceRecommendations
    });

  } catch (error) {
    Logger.error("Error in on-demand citation generation", {
      error,
      messageId: messageId || "unknown",
      chatId: chatId || "unknown"
    });

    return NextResponse.json({
      success: false,
      messageId: messageId || "",
      citationCount: 0,
      documentStatus: [],
      processingTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * Checks the processing status of all documents associated with a message
 */
async function checkDocumentProcessingStatus(messageId: string): Promise<DocumentProcessingStatus[]> {
  try {
    const associatedDocs = await db
      .select({
        documentId: sourceDocuments.id,
        filename: sourceDocuments.filename,
        markdown: sourceDocuments.extractedText
      })
      .from(messageDocuments)
      .innerJoin(sourceDocuments, eq(messageDocuments.sourceDocumentId, sourceDocuments.id))
      .where(eq(messageDocuments.messageId, messageId));

    return associatedDocs.map(doc => {
      const hasMarkdown = Boolean(doc.markdown && doc.markdown.length > 0);
      // For now, assume chunks exist if markdown exists (we can improve this later)
      const hasChunks = hasMarkdown;

      return {
        documentId: doc.documentId,
        filename: doc.filename || 'Unknown Document',
        isProcessed: hasMarkdown && hasChunks,
        hasMarkdown,
        hasChunks,
        processingError: undefined // No metadata field available
      };
    });
  } catch (error) {
    Logger.error("Error checking document processing status", { error, messageId });
    return [];
  }
}

/**
 * Attempts to process unprocessed documents
 */
async function processUnprocessedDocuments(unprocessedDocs: DocumentProcessingStatus[]): Promise<void> {
  const processingPromises = unprocessedDocs.map(async (doc) => {
    try {
      Logger.info("Attempting to process document", { 
        documentId: doc.documentId, 
        filename: doc.filename 
      });

      // Get document URL from database
      const docRecord = await db
        .select({ url: sourceDocuments.url, filename: sourceDocuments.filename })
        .from(sourceDocuments)
        .where(eq(sourceDocuments.id, doc.documentId))
        .limit(1);

      if (docRecord.length === 0) {
        Logger.warn("Document record not found", { documentId: doc.documentId });
        return;
      }

      const { url, filename } = docRecord[0];

      if (!url) {
        Logger.warn("Document missing URL", {
          documentId: doc.documentId,
          hasUrl: !!url
        });
        return;
      }

      // Infer content type from filename
      const contentType = filename?.toLowerCase().endsWith('.pdf') ? 'application/pdf' : 'application/octet-stream';

      // Process document for markdown and chunking
      const result = await DocumentMarkdownProcessor.processDocument(
        doc.documentId,
        url,
        contentType
      );

      if (result.success) {
        Logger.info("Document processing completed", {
          documentId: doc.documentId,
          pagesProcessed: result.pagesProcessed
        });
      } else {
        Logger.error("Document processing failed", {
          documentId: doc.documentId,
          error: result.error
        });
      }
    } catch (error) {
      Logger.error("Error processing document", {
        error,
        documentId: doc.documentId,
        filename: doc.filename
      });
    }
  });

  await Promise.allSettled(processingPromises);
}

/**
 * Retrieves stored images from documents that were actually cited
 * Uses optimized flow: images were extracted once during document processing and stored
 * Only retrieves images from documents that appear in the citations
 */
async function getStoredImagesFromCitedDocuments(
  messageId: string,
  citations: Array<{ documentId?: string; sourceId?: string; [key: string]: any }>
): Promise<Array<{ mime_type: string; url: string }>> {
  try {
    Logger.info("Starting stored image retrieval for Cites to your documents", {
      messageId,
      citationCount: citations.length,
      optimizedFlow: 'retrieve-stored-images'
    });

    // Extract unique document IDs from citations
    const citedDocumentIds = new Set<string>();
    citations.forEach(citation => {
      if (citation.documentId) {
        citedDocumentIds.add(citation.documentId);
      }
      if (citation.sourceId) {
        citedDocumentIds.add(citation.sourceId);
      }
    });

    if (citedDocumentIds.size === 0) {
      Logger.info("No document IDs found in citations", { messageId, citationCount: citations.length });
      return [];
    }

    Logger.info("Retrieving stored images for cited documents", {
      messageId,
      citedDocumentIds: Array.from(citedDocumentIds),
      citedDocumentCount: citedDocumentIds.size,
    });

    // Use DocumentMarkdownProcessor to retrieve stored images
    const { DocumentMarkdownProcessor } = await import("@/lib/services/document-markdown-processor");
    const storedImages = await DocumentMarkdownProcessor.getStoredImagesForCitedDocuments(
      Array.from(citedDocumentIds)
    );

    Logger.info("Retrieved stored images for Cites to Your Documents display", {
      messageId,
      citedDocumentIds: Array.from(citedDocumentIds),
      imageCount: storedImages.length,
      source: "enhanced-doc-reader-storage",
      note: "same-images-used-for-inline-citations-ocr"
    });

    if (storedImages.length === 0) {
      Logger.warn("No stored images found for cited documents, falling back to extraction", {
        messageId,
        citedDocumentIds: Array.from(citedDocumentIds)
      });
      // Fallback to extraction if no stored images found
      return await extractRelevantImagesFromCitedDocuments(messageId, citations);
    }

    Logger.info("Retrieved stored images for cited documents", {
      messageId,
      totalImages: storedImages.length,
      citedDocumentCount: citedDocumentIds.size,
      optimizedFlow: 'stored-images-retrieved',
    });

    // Return only the required fields for the API response
    return storedImages.map(image => ({
      mime_type: image.mime_type,
      url: image.url
    }));

  } catch (error) {
    Logger.error("Error in getStoredImagesFromCitedDocuments", {
      error,
      messageId,
      citationCount: citations.length
    });
    return [];
  }
}

/**
 * Fallback function: Retrieves stored images from associated documents when no citations exist
 * Uses optimized flow: retrieves stored images instead of extracting again
 */
async function getStoredImagesFromAssociatedDocuments(
  messageId: string,
  maxDocuments: number = 3
): Promise<Array<{ mime_type: string; url: string }>> {
  try {
    Logger.info("Starting fallback stored image retrieval from associated documents", {
      messageId,
      maxDocuments,
      optimizedFlow: 'retrieve-stored-images-fallback'
    });

    // Get documents associated with the message (limited)
    const associatedDocs = await db
      .select({
        documentId: sourceDocuments.id,
        filename: sourceDocuments.filename,
      })
      .from(messageDocuments)
      .innerJoin(sourceDocuments, eq(messageDocuments.sourceDocumentId, sourceDocuments.id))
      .where(eq(messageDocuments.messageId, messageId))
      .limit(maxDocuments);

    if (associatedDocs.length === 0) {
      Logger.info("No associated documents found for fallback retrieval", { messageId });
      return [];
    }

    const documentIds = associatedDocs.map(doc => doc.documentId);

    Logger.info("Found associated documents for fallback stored image retrieval", {
      messageId,
      documentCount: associatedDocs.length,
      maxDocuments,
      documentIds,
    });

    // Use DocumentMarkdownProcessor to retrieve stored images
    const { DocumentMarkdownProcessor } = await import("@/lib/services/document-markdown-processor");
    const storedImages = await DocumentMarkdownProcessor.getStoredImagesForCitedDocuments(documentIds);

    if (storedImages.length === 0) {
      Logger.warn("No stored images found for associated documents, falling back to extraction", {
        messageId,
        documentIds
      });
      // Fallback to extraction if no stored images found
      return await extractRelevantImagesFromAssociatedDocuments(messageId, maxDocuments);
    }

    // For fallback, only return first page per document to minimize data
    const limitedImages = storedImages.slice(0, maxDocuments);

    Logger.info("Fallback stored image retrieval completed", {
      messageId,
      totalImages: limitedImages.length,
      documentsProcessed: associatedDocs.length,
      optimizedFlow: 'stored-images-retrieved-fallback',
    });

    // Return only the required fields for the API response
    return limitedImages.map(image => ({
      mime_type: image.mime_type,
      url: image.url
    }));

  } catch (error) {
    Logger.error("Error in getStoredImagesFromAssociatedDocuments", {
      error,
      messageId,
      maxDocuments
    });
    return [];
  }
}

/**
 * Legacy fallback: Extracts images from cited documents when stored images are not available
 * This is kept as a fallback for cases where stored images are missing
 */
async function extractRelevantImagesFromCitedDocuments(
  messageId: string,
  citations: Array<{ documentId?: string; sourceId?: string; [key: string]: any }>
): Promise<Array<{ mime_type: string; url: string }>> {
  try {
    Logger.warn("Using legacy image extraction fallback for cited documents", {
      messageId,
      citationCount: citations.length,
      reason: 'stored-images-not-available'
    });

    // Extract unique document IDs from citations
    const citedDocumentIds = new Set<string>();
    citations.forEach(citation => {
      if (citation.documentId) {
        citedDocumentIds.add(citation.documentId);
      }
      if (citation.sourceId) {
        citedDocumentIds.add(citation.sourceId);
      }
    });

    if (citedDocumentIds.size === 0) {
      return [];
    }

    // Get only the documents that were actually cited
    const citedDocs = await db
      .select({
        documentId: sourceDocuments.id,
        filename: sourceDocuments.filename,
        url: sourceDocuments.url
      })
      .from(sourceDocuments)
      .where(sql`${sourceDocuments.id} = ANY(${Array.from(citedDocumentIds)})`);

    if (citedDocs.length === 0) {
      return [];
    }

    const imageExtractionPromises = citedDocs.map(async (doc) => {
      try {
        if (!doc.url) {
          return [];
        }

        // Use Enhanced Doc Reader for image extraction
        const pages = await EnhancedDocReaderImageIntegration.processPdfToImages(
          doc.documentId,
          doc.url
        );

        if (!pages || pages.length === 0) {
          return [];
        }

        // Limit to first page for Enhanced Doc Reader extraction
        const firstPage = pages[0];

        return [{
          mime_type: "image/jpeg",
          url: firstPage.imageBase64,
          documentId: doc.documentId,
          filename: doc.filename,
          pageNumber: firstPage.pageNumber || 1,
        }];

      } catch (error) {
        Logger.error("Error in Enhanced Doc Reader image extraction", {
          error,
          documentId: doc.documentId,
          messageId,
          extractionMethod: 'enhanced-doc-reader'
        });
        return [];
      }
    });

    const imageResults = await Promise.allSettled(imageExtractionPromises);
    const allImages = imageResults
      .filter((result): result is PromiseFulfilledResult<any[]> => result.status === 'fulfilled')
      .flatMap(result => result.value)
      .filter(image => image && image.url);

    Logger.info("Enhanced Doc Reader image extraction completed", {
      messageId,
      totalImages: allImages.length,
      citedDocumentsProcessed: citedDocs.length,
      extractionMethod: 'enhanced-doc-reader'
    });

    return allImages.map(image => ({
      mime_type: image.mime_type,
      url: image.url
    }));

  } catch (error) {
    Logger.error("Error in Enhanced Doc Reader image extraction", {
      error,
      messageId,
      citationCount: citations.length,
      extractionMethod: 'enhanced-doc-reader'
    });
    return [];
  }
}

/**
 * Enhanced Doc Reader: Extracts images from associated documents when stored images are not available
 * Uses Enhanced Doc Reader image extraction for reliable document processing
 */
async function extractRelevantImagesFromAssociatedDocuments(
  messageId: string,
  maxDocuments: number = 3
): Promise<Array<{ mime_type: string; url: string }>> {
  try {
    Logger.warn("Using Enhanced Doc Reader image extraction for associated documents", {
      messageId,
      maxDocuments,
      reason: 'stored-images-not-available',
      extractionMethod: 'enhanced-doc-reader'
    });

    // Get documents associated with the message (limited)
    const associatedDocs = await db
      .select({
        documentId: sourceDocuments.id,
        filename: sourceDocuments.filename,
        url: sourceDocuments.url
      })
      .from(messageDocuments)
      .innerJoin(sourceDocuments, eq(messageDocuments.sourceDocumentId, sourceDocuments.id))
      .where(eq(messageDocuments.messageId, messageId))
      .limit(maxDocuments);

    if (associatedDocs.length === 0) {
      return [];
    }

    const imageExtractionPromises = associatedDocs.map(async (doc) => {
      try {
        if (!doc.url) {
          return [];
        }

        // Use Enhanced Doc Reader for image extraction
        const pages = await EnhancedDocReaderImageIntegration.processPdfToImages(
          doc.documentId,
          doc.url
        );

        if (!pages || pages.length === 0) {
          return [];
        }

        // For Enhanced Doc Reader extraction, only return first page per document
        const firstPage = pages[0];

        return [{
          mime_type: "image/jpeg",
          url: firstPage.imageBase64,
          documentId: doc.documentId,
          filename: doc.filename,
          pageNumber: firstPage.pageNumber || 1,
        }];

      } catch (error) {
        Logger.error("Error in Enhanced Doc Reader image extraction", {
          error,
          documentId: doc.documentId,
          messageId,
          extractionMethod: 'enhanced-doc-reader'
        });
        return [];
      }
    });

    const imageResults = await Promise.allSettled(imageExtractionPromises);
    const allImages = imageResults
      .filter((result): result is PromiseFulfilledResult<any[]> => result.status === 'fulfilled')
      .flatMap(result => result.value)
      .filter(image => image && image.url);

    Logger.info("Enhanced Doc Reader image extraction completed", {
      messageId,
      totalImages: allImages.length,
      documentsProcessed: associatedDocs.length,
      extractionMethod: 'enhanced-doc-reader'
    });

    return allImages.map(image => ({
      mime_type: image.mime_type,
      url: image.url
    }));

  } catch (error) {
    Logger.error("Error in Enhanced Doc Reader image extraction", {
      error,
      messageId,
      maxDocuments,
      extractionMethod: 'enhanced-doc-reader'
    });
    return [];
  }
}
