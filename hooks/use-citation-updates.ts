import { useEffect, useRef, useState, useCallback } from "react";
import { Logger } from "@/lib/utils/Logger";
import { toast } from "sonner";

interface CitationStatus {
  status: "pending" | "processing" | "completed" | "failed";
  startedAt?: string;
  completedAt?: string;
  error?: string;
  citationCount?: number;
  processingTimeMs?: number;
}

interface MessageUpdate {
  id: string;
  content: string;
  metadata: {
    inlineCitations?: {
      triggered: boolean;
      enabled: boolean;
      citationCount?: number;
      asyncStatus?: CitationStatus;
    };
  };
  updatedAt: string;
}

interface UseCitationUpdatesOptions {
  messageId: string;
  enabled?: boolean;
  onCitationComplete?: (messageUpdate: MessageUpdate) => void;
  pollingInterval?: number;
  maxPollingDuration?: number;
}

/**
 * Hook for polling citation updates for a specific message
 */
export function useCitationUpdates({
  messageId,
  enabled = true,
  onCitationComplete,
  pollingInterval = 1000, // Poll every 1 second for better responsiveness
  maxPollingDuration = 60000, // Stop polling after 1 minute
}: UseCitationUpdatesOptions) {
  const [citationStatus, setCitationStatus] = useState<CitationStatus | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<MessageUpdate | null>(null);

  const pollingStartTime = useRef<number | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastKnownContent = useRef<string>("");
  const lastKnownStatus = useRef<string>("");
  const hasCompletedOnce = useRef<boolean>(false);
  const onCitationCompleteRef = useRef(onCitationComplete);

  // Update the ref when the callback changes
  useEffect(() => {
    onCitationCompleteRef.current = onCitationComplete;
  }, [onCitationComplete]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsPolling(false);
    pollingStartTime.current = null;
    Logger.info("Citation polling stopped", { messageId });
  }, [messageId]);

  const startPolling = useCallback(() => {
    if (!enabled || !messageId || isPolling) return;

    Logger.info("Starting citation polling", { messageId });
    setIsPolling(true);
    pollingStartTime.current = Date.now();

    const poll = async () => {
      try {
        const response = await fetch(`/api/messages/${messageId}`, {
          headers: {
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const messageUpdate: MessageUpdate = await response.json();
        setLastUpdate(messageUpdate);

        const asyncStatus = messageUpdate.metadata?.inlineCitations?.asyncStatus;
        const currentContent = messageUpdate.content;
        const currentStatusStr = asyncStatus?.status || "none";

        // Enhanced logging for debugging
        Logger.info("Citation polling update", {
          messageId,
          currentStatus: currentStatusStr,
          lastKnownStatus: lastKnownStatus.current,
          contentChanged: currentContent !== lastKnownContent.current,
          contentLength: typeof currentContent === 'string' ? currentContent.length : 0,
          citationCount: asyncStatus?.citationCount || 0,
          hasAsyncStatus: !!asyncStatus
        });

        if (asyncStatus) {
          setCitationStatus(asyncStatus);

          // Update tracking refs
          lastKnownStatus.current = currentStatusStr;

          // Check if citations were completed
          if (asyncStatus.status === "completed" && !hasCompletedOnce.current) {
            hasCompletedOnce.current = true;

            Logger.info("Citations completed for message", {
              messageId,
              citationCount: asyncStatus.citationCount,
              processingTimeMs: asyncStatus.processingTimeMs,
              contentChanged: currentContent !== lastKnownContent.current
            });

            // Always call completion callback when status changes to completed
            if (onCitationCompleteRef.current) {
              Logger.info("Calling onCitationComplete callback", { messageId });
              onCitationCompleteRef.current(messageUpdate);
            }

            // Update content tracking
            lastKnownContent.current = currentContent;

            // Show toast notification if citations were actually added
            if (asyncStatus.citationCount && asyncStatus.citationCount > 0) {
              toast.success(
                `Inline citations added (${asyncStatus.citationCount} references)`,
                {
                  duration: 4000,
                  action: {
                    label: "View",
                    onClick: () => {
                      // Scroll to the message or highlight it
                      const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                      if (messageElement) {
                        messageElement.scrollIntoView({ behavior: "smooth", block: "center" });
                      }
                    },
                  },
                }
              );
            }

            stopPolling();
            return;
          }

          // Check if citations failed
          if (asyncStatus.status === "failed") {
            Logger.warn("Citation processing failed", {
              messageId,
              error: asyncStatus.error,
            });
            stopPolling();
            return;
          }
        }

        // Check if we've been polling too long
        if (pollingStartTime.current && Date.now() - pollingStartTime.current > maxPollingDuration) {
          Logger.info("Citation polling timeout", { messageId });
          stopPolling();
          return;
        }

      } catch (error) {
        Logger.error("Error polling for citation updates", { messageId, error });
        // Continue polling on error, but with exponential backoff
      }
    };

    // Start immediate poll
    poll();

    // Set up interval polling
    intervalRef.current = setInterval(poll, pollingInterval);
  }, [enabled, messageId, isPolling, pollingInterval, maxPollingDuration]);

  // Start polling when enabled and messageId changes
  useEffect(() => {
    if (enabled && messageId) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => stopPolling();
  }, [messageId, enabled]); // Removed startPolling and stopPolling from dependencies

  // Cleanup on unmount
  useEffect(() => {
    return () => stopPolling();
  }, []); // Empty dependency array for cleanup only

  return {
    citationStatus,
    isPolling,
    lastUpdate,
    startPolling,
    stopPolling,
  };
}

/**
 * Hook for managing citation updates for multiple messages
 */
export function useMultiMessageCitationUpdates(
  messageIds: string[],
  onCitationComplete?: (messageId: string, messageUpdate: MessageUpdate) => void
) {
  const [citationStatuses, setCitationStatuses] = useState<Record<string, CitationStatus>>({});
  const [pollingMessages, setPollingMessages] = useState<Set<string>>(new Set());

  // Track which messages need citation polling
  useEffect(() => {
    const needsPolling = new Set<string>();

    messageIds.forEach(messageId => {
      const status = citationStatuses[messageId];
      if (!status || (status.status === "pending" || status.status === "processing")) {
        needsPolling.add(messageId);
      }
    });

    setPollingMessages(needsPolling);
  }, [messageIds, citationStatuses]);

  const handleCitationComplete = (messageId: string) => (messageUpdate: MessageUpdate) => {
    // Update status for this message
    const asyncStatus = messageUpdate.metadata?.inlineCitations?.asyncStatus;
    if (asyncStatus) {
      setCitationStatuses(prev => ({
        ...prev,
        [messageId]: asyncStatus,
      }));
    }

    // Call the callback
    if (onCitationComplete) {
      onCitationComplete(messageId, messageUpdate);
    }
  };

  return {
    citationStatuses,
    pollingMessages: Array.from(pollingMessages),
    handleCitationComplete,
  };
}
