import useSWR from 'swr';
import { fetcher } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { Logger } from '@/lib/utils/Logger';

export function useSourceDocuments(chatId: string | null) {
  const { data, error, mutate } = useSWR(
    chatId ? `/api/source-documents?chatId=${chatId}` : null,
    fetcher,
    {
      // Refresh data more frequently to ensure we have the latest documents
      refreshInterval: 5000, // Refresh every 5 seconds
      revalidateOnFocus: true,
      dedupingInterval: 2000 // Deduplicate requests within 2 seconds
    }
  );

  // State to hold combined documents from API and localStorage
  const [combinedDocuments, setCombinedDocuments] = useState<any[]>([]);

  useEffect(() => {
    if (!chatId) {
      setCombinedDocuments([]);
      return;
    }

    // Start with API data
    const apiDocuments = data || [];

    if (apiDocuments.length > 0) {
      Logger.info("Retrieved documents from API:", {
        count: apiDocuments.length,
        chatId
      });
    }

    // Try to get additional documents from localStorage
    try {
      const localStorageKey = `chat-files-${chatId}`;
      const localStorageFilesStr = localStorage.getItem(localStorageKey);

      if (!localStorageFilesStr) {
        // No localStorage files, just use API documents
        setCombinedDocuments(apiDocuments);
        return;
      }

      const localStorageFiles = JSON.parse(localStorageFilesStr);

      if (localStorageFiles.length > 0) {
        Logger.info("Retrieved documents from localStorage:", {
          count: localStorageFiles.length,
          chatId
        });
      }

      // Convert localStorage files to the same format as API documents
      const localStorageDocuments = localStorageFiles.map((file: any) => {
        // Ensure we have a valid document ID
        const documentId = file.document_id || file.id || file.sourceDocumentId;

        if (!documentId) {
          Logger.warn("File missing document ID:", file);
          return null;
        }

        return {
          id: documentId,
          filename: file.name || file.fileName || 'Untitled Document',
          url: file.url || file.fileUrl,
          contentType: file.contentType || file.fileType || 'application/octet-stream',
          createdAt: file.createdAt || new Date().toISOString(),
          userId: 'current-user', // Placeholder
          chatId: chatId,
          // Add a flag to indicate this came from localStorage
          fromLocalStorage: true,
          // Add message ID if available
          messageId: file.messageId
        };
      }).filter(Boolean); // Remove any null entries

      // Combine API documents with localStorage documents, avoiding duplicates
      const apiDocIds = new Set(apiDocuments.map((doc: any) => doc.id));
      const uniqueLocalDocs = localStorageDocuments.filter(
        (doc: any) => !apiDocIds.has(doc.id)
      );

      if (uniqueLocalDocs.length > 0) {
        Logger.info("Adding unique localStorage documents:", {
          count: uniqueLocalDocs.length,
          chatId
        });
      }

      const combined = [...apiDocuments, ...uniqueLocalDocs];
      setCombinedDocuments(combined);

      Logger.info("Combined documents set:", {
        total: combined.length,
        fromApi: apiDocuments.length,
        fromLocalStorage: uniqueLocalDocs.length,
        chatId
      });
    } catch (error) {
      Logger.error('Error combining documents with localStorage:', error);
      setCombinedDocuments(apiDocuments);
    }
  }, [chatId, data]);

  // Function to manually refresh the documents
  const refreshDocuments = async () => {
    // Logger.info("Manually refreshing documents for chat:", chatId);

    // First, refresh the API data
    const result = await mutate();

    // Then, force a refresh of the combined documents by re-processing localStorage
    if (chatId) {
      try {
        const localStorageKey = `chat-files-${chatId}`;
        const localStorageFilesStr = localStorage.getItem(localStorageKey);

        if (localStorageFilesStr) {
          // Force a refresh by updating the state with new array reference
          setCombinedDocuments(prev => [...prev]);

          Logger.info("Triggered refresh of combined documents from localStorage", { chatId });
        }
      } catch (error) {
        Logger.error('Error refreshing documents from localStorage:', error);
      }
    }

    return result;
  };

  return {
    documents: combinedDocuments,
    isLoading: !error && !data,
    isError: error,
    refresh: refreshDocuments
  };
}