"use client";

import { updateChatVisibility } from "@/app/(chat)/actions";
import { VisibilityType } from "@/components/visibility-selector";
import { Chat } from "@/lib/db/schema";
import { DEFAULT_CHAT_VISIBILITY } from "@/lib/types";
import { useMemo } from "react";
import useSWR, { useSWRConfig } from "swr";
import { logEvent } from "@/lib/analytics/events-client";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";
import { useUser } from "@/contexts/UserContext";

export function useChatVisibility({
  chatId,
  initialVisibility,
}: {
  chatId: string;
  initialVisibility: VisibilityType;
}) {
  const { mutate, cache } = useSWRConfig();
  const history: Array<Chat> = cache.get("/api/history")?.data;
  const sidebarData = cache.get("/api/chat-org/sidebar-data")?.data;
  const user = useUser();

  const { data: localVisibility, mutate: setLocalVisibility } = useSWR(
    `${chatId}-visibility`,
    null,
    {
      fallbackData: initialVisibility,
    }
  );

  const visibilityType = useMemo(() => {
    // First check sidebar data
    if (sidebarData?.chats) {
      const chat = sidebarData.chats.find((chat: Chat) => chat.id === chatId);
      if (chat) return chat.visibility;
    }

    // Then check history
    if (history) {
      const chat = history.find((chat) => chat.id === chatId);
      if (chat) return chat.visibility;
    }

    // Fall back to local visibility
    return localVisibility;
  }, [sidebarData, history, chatId, localVisibility]);

  const setVisibilityType = (updatedVisibilityType: VisibilityType) => {
    setLocalVisibility(updatedVisibilityType);

    // Log the Chat Share event when a chat is made public
    if (updatedVisibilityType === "public" && visibilityType === "private") {
      logEvent(SiteBehaviorEvent.CHAT_SHARE, {
        userId: user?.id,
        userEmail: user?.email,
        isAdmin: user?.isAdmin,
        chatId,
        previousVisibility: visibilityType,
        newVisibility: updatedVisibilityType,
      });
    }

    // Update history cache
    mutate<Array<Chat>>(
      "/api/history",
      (history) => {
        return history
          ? history.map((chat) => {
              if (chat.id === chatId) {
                return {
                  ...chat,
                  visibility: updatedVisibilityType,
                };
              }
              return chat;
            })
          : [];
      },
      { revalidate: false }
    );

    // Update sidebar data cache
    mutate(
      "/api/chat-org/sidebar-data",
      (data: any) => {
        if (!data) return data;

        return {
          ...data,
          chats: data.chats.map((chat: Chat) => {
            if (chat.id === chatId) {
              return {
                ...chat,
                visibility: updatedVisibilityType,
              };
            }
            return chat;
          }),
          // Also update the visibility in pinnedChats if the chat is pinned
          pinnedChats: data.pinnedChats?.map((pinnedChat: any) => {
            if (pinnedChat.chatId === chatId) {
              return {
                ...pinnedChat,
                chat: {
                  ...pinnedChat.chat,
                  visibility: updatedVisibilityType,
                },
              };
            }
            return pinnedChat;
          }),
        };
      },
      { revalidate: false }
    );

    updateChatVisibility({
      chatId: chatId,
      visibility: updatedVisibilityType,
    });
  };

  return { visibilityType, setVisibilityType };
}
