import { db } from "../lib/db";
import { sql } from "drizzle-orm";

/**
 * Simple script to check database status for inline citations
 */
async function checkInlineCitationsDatabase() {
  try {
    console.log("🔍 Checking inline citations database status...\n");

    // 1. Check if tables exist
    console.log("📊 Checking tables...");
    const tables = await db.execute(sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name IN ('embeddings', 'source_documents', 'resources', 'message_documents')
      ORDER BY table_name
    `);

    console.log(`Found ${tables.length} relevant tables:`);
    tables.forEach(table => console.log(`  ✓ ${table.table_name}`));

    // 2. Check existing indexes
    console.log("\n🔗 Checking existing indexes...");
    const indexes = await db.execute(sql`
      SELECT indexname, tablename
      FROM pg_indexes
      WHERE tablename IN ('embeddings', 'source_documents', 'resources', 'message_documents')
      ORDER BY tablename, indexname
    `);

    console.log(`Found ${indexes.length} indexes:`);
    indexes.forEach(idx => console.log(`  ${idx.tablename}.${idx.indexname}`));

    // 3. Check vector extension
    console.log("\n🧮 Checking vector extension...");
    const extensions = await db.execute(sql`
      SELECT extname FROM pg_extension WHERE extname = 'vector'
    `);

    if (extensions.length > 0) {
      console.log("  ✓ Vector extension is installed");
    } else {
      console.log("  ❌ Vector extension is NOT installed");
    }

    // 4. Check table sizes and data
    console.log("\n📈 Checking table data...");
    const tableCounts = await Promise.all([
      db.execute(sql`SELECT COUNT(*) as count FROM embeddings`),
      db.execute(sql`SELECT COUNT(*) as count FROM source_documents`),
      db.execute(sql`SELECT COUNT(*) as count FROM resources`),
      db.execute(sql`SELECT COUNT(*) as count FROM message_documents`)
    ]);

    console.log(`  embeddings: ${tableCounts[0][0]?.count || 0} rows`);
    console.log(`  source_documents: ${tableCounts[1][0]?.count || 0} rows`);
    console.log(`  resources: ${tableCounts[2][0]?.count || 0} rows`);
    console.log(`  message_documents: ${tableCounts[3][0]?.count || 0} rows`);

    // 5. Check if optimization is needed
    const embeddingCount = parseInt(String(tableCounts[0][0]?.count || "0"));
    const hasVectorIndex = indexes.some(idx =>
      idx.tablename === 'embeddings' && String(idx.indexname).includes('embedding')
    );

    console.log("\n💡 Recommendations:");
    if (embeddingCount === 0) {
      console.log("  ⚠️  No embeddings data found - optimization not needed yet");
    } else if (!hasVectorIndex) {
      console.log("  🚀 Vector index missing - optimization recommended");
      console.log("  📝 Run: pnpm tsx scripts/apply-inline-citations-indexes.ts");
    } else {
      console.log("  ✅ Database appears to be optimized");
    }

    console.log("\n✅ Database check completed successfully");

  } catch (error) {
    console.error("❌ Database check failed:", error);
    process.exit(1);
  }
}

// Run the check
if (require.main === module) {
  checkInlineCitationsDatabase()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

export { checkInlineCitationsDatabase };
