#!/usr/bin/env tsx
/**
 * Test Script: RAG Exports Verification
 * 
 * This script tests that the RAG file exports are working correctly
 * and there are no duplicate export errors.
 * 
 * Usage: npx tsx scripts/test-rag-exports.ts
 */

async function testRAGExports() {
  console.log("🧪 Testing RAG exports...");
  
  try {
    // Test importing the RAG module
    const ragModule = await import("../lib/ai/rag");
    
    console.log("✅ RAG module imported successfully");
    
    // Check that all expected exports are available
    const expectedExports = [
      'Document',
      'DocumentChunk', 
      'RAGPipeline',
      'refineUserPromptForRAG'
    ];
    
    const availableExports = Object.keys(ragModule);
    console.log(`📦 Available exports: ${availableExports.join(', ')}`);
    
    let allExportsFound = true;
    
    for (const expectedExport of expectedExports) {
      if (availableExports.includes(expectedExport)) {
        console.log(`✅ ${expectedExport} - Found`);
      } else {
        console.log(`❌ ${expectedExport} - Missing`);
        allExportsFound = false;
      }
    }
    
    // Test that we can instantiate RAGPipeline
    try {
      const pipeline = new ragModule.RAGPipeline();
      console.log("✅ RAGPipeline can be instantiated");
    } catch (error) {
      console.log(`❌ RAGPipeline instantiation failed: ${error instanceof Error ? error.message : String(error)}`);
      allExportsFound = false;
    }
    
    // Test that refineUserPromptForRAG is a function
    if (typeof ragModule.refineUserPromptForRAG === 'function') {
      console.log("✅ refineUserPromptForRAG is a function");
    } else {
      console.log("❌ refineUserPromptForRAG is not a function");
      allExportsFound = false;
    }
    
    if (allExportsFound) {
      console.log("\n🎉 All RAG exports are working correctly!");
      console.log("✨ No duplicate export errors detected");
      return true;
    } else {
      console.log("\n❌ Some RAG exports are missing or incorrect");
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Failed to import RAG module: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

async function main() {
  console.log("🔧 RAG Exports Test");
  console.log("===================");
  console.log("");

  const testsPassed = await testRAGExports();

  console.log("\n🎯 Overall Result:");
  console.log("==================");
  
  if (testsPassed) {
    console.log("✅ ALL TESTS PASSED");
    console.log("✨ The RAG file exports are working correctly:");
    console.log("   • No duplicate export errors");
    console.log("   • All expected exports are available");
    console.log("   • RAGPipeline can be instantiated");
    console.log("   • refineUserPromptForRAG function is accessible");
  } else {
    console.log("❌ SOME TESTS FAILED");
    console.log("⚠️  Please review the failed tests above and address any issues.");
  }

  process.exit(testsPassed ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as testRAGExports };
