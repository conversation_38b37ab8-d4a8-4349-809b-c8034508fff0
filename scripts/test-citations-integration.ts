#!/usr/bin/env tsx
/**
 * Test Runner: Citations Integration Verification
 * 
 * Usage: npx tsx scripts/test-citations-integration.ts [messageId] [chatId] [documentId1,documentId2,...]
 * 
 * Example:
 * npx tsx scripts/test-citations-integration.ts msg_123 chat_456 doc_789,doc_abc
 */

import { CitationsIntegrationVerifier } from "./verify-citations-integration";
import { Logger } from "../lib/utils/Logger";

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 3) {
    console.log(`
Usage: npx tsx scripts/test-citations-integration.ts [messageId] [chatId] [documentId1,documentId2,...]

Example:
npx tsx scripts/test-citations-integration.ts msg_123 chat_456 doc_789,doc_abc

This script verifies:
1. "Cites to Your Documents" images are stored and accessible
2. Inline citations pipeline uses the same stored images for OCR
3. No race conditions exist between the two features
4. Manual triggering works correctly
    `);
    process.exit(1);
  }

  const [messageId, chatId, documentIdsStr] = args;
  const documentIds = documentIdsStr.split(',').map(id => id.trim());

  console.log("🔍 Starting Citations Integration Verification...");
  console.log(`📧 Message ID: ${messageId}`);
  console.log(`💬 Chat ID: ${chatId}`);
  console.log(`📄 Document IDs: ${documentIds.join(', ')}`);
  console.log("");

  try {
    const { overall, results } = await CitationsIntegrationVerifier.runFullVerification(
      messageId,
      chatId,
      documentIds
    );

    console.log("📊 Verification Results:");
    console.log("========================");

    Object.entries(results).forEach(([testName, result]) => {
      const status = result.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${testName}: ${result.message}`);
      
      if (result.details) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
      console.log("");
    });

    console.log("🎯 Overall Result:");
    console.log("==================");
    if (overall) {
      console.log("✅ ALL TESTS PASSED");
      console.log("✨ The integration is working correctly:");
      console.log("   • 'Cites to Your Documents' images are properly stored");
      console.log("   • Inline citations use the same stored images for OCR");
      console.log("   • Manual triggering is properly configured");
      console.log("   • No race conditions detected");
    } else {
      console.log("❌ SOME TESTS FAILED");
      console.log("⚠️  Please review the failed tests above and address any issues.");
    }

    process.exit(overall ? 0 : 1);

  } catch (error) {
    console.error("💥 Verification failed with error:", error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as testCitationsIntegration };
