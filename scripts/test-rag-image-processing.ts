#!/usr/bin/env tsx
/**
 * Test Script: RAG Image Processing Fix
 * 
 * This script tests the fix for the "Invalid URL" error in RAGProcessor.convertUrlToBase64
 * 
 * Usage: npx tsx scripts/test-rag-image-processing.ts
 */

import { Logger } from "../lib/utils/Logger";

// Mock the RAGProcessor class for testing
class TestRAGProcessor {
  private static base64Cache: Map<string, string> = new Map();

  /**
   * Helper method to check if a string is valid base64
   */
  private static isBase64String(str: string): boolean {
    try {
      // Basic base64 pattern check
      const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Pattern.test(str)) {
        return false;
      }
      
      // Try to decode to verify it's valid base64
      if (typeof window !== 'undefined') {
        // Browser environment
        window.atob(str);
      } else {
        // Node.js environment
        Buffer.from(str, 'base64').toString('base64') === str;
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Fixed convertUrlToBase64 method
   */
  private static async convertUrlToBase64(url: string): Promise<string> {
    // Checking cache first
    if (this.base64Cache.has(url)) {
      return this.base64Cache.get(url)!;
    }

    try {
      // Validate URL format first
      if (!url || typeof url !== 'string') {
        Logger.warn("Invalid URL provided to convertUrlToBase64:", { url, type: typeof url });
        return "";
      }

      // Check if it's already base64 data
      if (url.startsWith('data:')) {
        Logger.debug("URL is already a data URI, extracting base64 part");
        const base64Part = url.split(',')[1];
        if (base64Part) {
          this.base64Cache.set(url, base64Part);
          return base64Part;
        }
      }

      // Check if it's already base64 data without data URI prefix
      // This handles the case where image_data.data contains raw base64
      if (this.isBase64String(url)) {
        Logger.debug("Input is already base64 data, returning as-is");
        this.base64Cache.set(url, url);
        return url;
      }

      // Validate that it's a proper URL
      try {
        new URL(url);
      } catch (urlError) {
        Logger.warn("Invalid URL format, treating as base64 data:", { 
          url: url.substring(0, 50) + "...", 
          error: urlError.message 
        });
        // If it's not a valid URL, assume it's base64 data
        if (url.length > 0) {
          this.base64Cache.set(url, url);
          return url;
        }
        return "";
      }

      // Normalizing URL to prevent double encoding issues
      const normalizedUrl = decodeURIComponent(url);

      const response = await fetch(normalizedUrl);

      if (!response.ok) {
        Logger.warn("Failed to fetch URL:", { url: normalizedUrl, status: response.status, statusText: response.statusText });
        return "";
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64Data = buffer.toString("base64");

      // Cache the result
      this.base64Cache.set(url, base64Data);
      return base64Data;
    } catch (error) {
      Logger.error("Error converting URL to base64:", { url, error: error instanceof Error ? error.message : String(error) });
      return "";
    }
  }

  /**
   * Test the processImageChunks method
   */
  public static async testProcessImageChunks(
    imageChunks: Array<{ mime_type: string; data: string }>
  ): Promise<Array<{ mime_type: string; data: string; url: string }>> {
    // Filter out invalid chunks first
    const validChunks = imageChunks.filter(chunk => {
      if (!chunk || !chunk.data || typeof chunk.data !== 'string') {
        Logger.warn("Skipping invalid image chunk:", chunk);
        return false;
      }
      return true;
    });

    Logger.debug("Processing image chunks:", {
      totalChunks: imageChunks.length,
      validChunks: validChunks.length,
      skippedChunks: imageChunks.length - validChunks.length
    });

    // Processing all valid images in parallel
    const processedChunks = await Promise.all(
      validChunks.map(async (chunk) => {
        try {
          Logger.debug("Processing image chunk:", { 
            mime_type: chunk.mime_type, 
            dataLength: chunk.data.length,
            dataPreview: chunk.data.substring(0, 50) + "...",
            isDataUri: chunk.data.startsWith('data:'),
            isBase64: chunk.data.match(/^[A-Za-z0-9+/]*={0,2}$/) !== null
          });
          
          const base64Data = await this.convertUrlToBase64(chunk.data);

          if (!base64Data) {
            Logger.warn("Failed to convert chunk to base64, skipping:", { 
              mime_type: chunk.mime_type,
              dataPreview: chunk.data.substring(0, 50) + "..."
            });
            return null;
          }

          return {
            mime_type: chunk.mime_type,
            data: base64Data,
            url: chunk.data,
          };
        } catch (error) {
          Logger.error("Error processing image chunk:", { 
            chunkMimeType: chunk.mime_type,
            dataPreview: chunk.data ? chunk.data.substring(0, 50) + "..." : "null",
            error: error instanceof Error ? error.message : String(error) 
          });
          return null;
        }
      })
    );

    // Filter out any failed conversions
    return processedChunks.filter((chunk) => chunk !== null) as Array<{
      mime_type: string;
      data: string;
      url: string;
    }>;
  }
}

async function main() {
  console.log("🧪 Testing RAG Image Processing Fix...");
  console.log("");

  // Test cases
  const testCases = [
    {
      name: "Valid base64 data (typical case from SearchService)",
      data: {
        mime_type: "image/jpeg",
        data: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
      }
    },
    {
      name: "Data URI format",
      data: {
        mime_type: "image/png",
        data: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
      }
    },
    {
      name: "Invalid URL (this was causing the error)",
      data: {
        mime_type: "image/jpeg",
        data: "not-a-valid-url-but-should-be-treated-as-base64"
      }
    },
    {
      name: "Empty data",
      data: {
        mime_type: "image/jpeg",
        data: ""
      }
    }
  ];

  let allTestsPassed = true;

  for (const testCase of testCases) {
    console.log(`🔍 Testing: ${testCase.name}`);
    
    try {
      const result = await TestRAGProcessor.testProcessImageChunks([testCase.data]);
      
      if (testCase.data.data === "") {
        // Empty data should result in no processed chunks
        if (result.length === 0) {
          console.log("✅ PASS - Empty data handled correctly");
        } else {
          console.log("❌ FAIL - Empty data should result in no processed chunks");
          allTestsPassed = false;
        }
      } else {
        // Non-empty data should be processed successfully
        if (result.length === 1 && result[0].mime_type === testCase.data.mime_type) {
          console.log("✅ PASS - Data processed successfully");
        } else {
          console.log("❌ FAIL - Data processing failed");
          console.log("   Expected 1 result, got:", result.length);
          allTestsPassed = false;
        }
      }
    } catch (error) {
      console.log("❌ FAIL - Error during processing:", error instanceof Error ? error.message : String(error));
      allTestsPassed = false;
    }
    
    console.log("");
  }

  console.log("🎯 Overall Result:");
  console.log("==================");
  if (allTestsPassed) {
    console.log("✅ ALL TESTS PASSED");
    console.log("✨ The RAG image processing fix is working correctly!");
    console.log("   • Base64 data is handled properly");
    console.log("   • Data URIs are processed correctly");
    console.log("   • Invalid URLs are treated as base64 data");
    console.log("   • No more 'Invalid URL' errors should occur");
  } else {
    console.log("❌ SOME TESTS FAILED");
    console.log("⚠️  Please review the failed tests above.");
  }

  process.exit(allTestsPassed ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as testRagImageProcessing };
