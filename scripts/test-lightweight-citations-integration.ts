#!/usr/bin/env tsx
/**
 * Test Script: Lightweight Citations Integration
 * 
 * This script tests the lightweight integration between inline citations
 * and existing services (Enhanced Doc Reader, RAG) without modifying
 * their core implementations.
 * 
 * Usage: npx tsx scripts/test-lightweight-citations-integration.ts
 */

import { Logger } from "../lib/utils/Logger";

// Mock test data
const mockExtractedImages = [
  {
    documentId: "doc-123",
    pageNumber: 1,
    imageBase64: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
    width: 800,
    height: 600,
    metadata: {
      scaleFactor: 2,
      quality: "high",
      renderingApproach: "pdf-to-canvas"
    }
  },
  {
    documentId: "doc-123",
    pageNumber: 2,
    imageBase64: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
    width: 800,
    height: 600,
    metadata: {
      scaleFactor: 2,
      quality: "high",
      renderingApproach: "pdf-to-canvas"
    }
  }
];

/**
 * Test the lightweight citations integration
 */
class LightweightCitationsIntegrationTest {
  /**
   * Test service hook registry functionality
   */
  static async testServiceHookRegistry(): Promise<boolean> {
    console.log("🧪 Testing Service Hook Registry...");
    
    try {
      const { ServiceHookRegistry } = await import("../lib/services/hooks/service-hook-registry");
      
      // Test hook registration
      ServiceHookRegistry.registerHook({
        id: 'test-hook',
        serviceId: 'test-service',
        operation: 'test-operation',
        priority: 10,
        handler: async (context) => {
          return { success: true, data: { received: context.data } };
        }
      });

      // Test hook execution
      const results = await ServiceHookRegistry.executeHooks({
        serviceId: 'test-service',
        operation: 'test-operation',
        data: { test: 'data' }
      });

      if (results.length === 1 && results[0].success) {
        console.log("✅ Service Hook Registry: Registration and execution working");
        
        // Cleanup
        ServiceHookRegistry.unregisterHook('test-hook');
        return true;
      } else {
        console.log("❌ Service Hook Registry: Hook execution failed");
        return false;
      }
    } catch (error) {
      console.log(`❌ Service Hook Registry: Error - ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Test inline citations hooks
   */
  static async testInlineCitationsHooks(): Promise<boolean> {
    console.log("\n🧪 Testing Inline Citations Hooks...");
    
    try {
      const { InlineCitationsHooks } = await import("../lib/services/hooks/inline-citations-hooks");
      const { ServiceHookRegistry } = await import("../lib/services/hooks/service-hook-registry");
      
      // Simulate Enhanced Doc Reader image extraction
      await ServiceHookRegistry.executeHooks({
        serviceId: 'enhanced-doc-reader',
        operation: 'images-extracted',
        data: {
          documentId: 'doc-123',
          pages: mockExtractedImages
        }
      });

      // Check if images were cached
      const cachedImages = InlineCitationsHooks.getCachedImages('doc-123');
      
      if (cachedImages && cachedImages.length === 2) {
        console.log("✅ Inline Citations Hooks: Image caching working");
        
        // Test cache statistics
        const stats = InlineCitationsHooks.getCacheStats();
        console.log(`   Cache stats: ${stats.cacheSize} entries`);
        
        return true;
      } else {
        console.log("❌ Inline Citations Hooks: Image caching failed");
        return false;
      }
    } catch (error) {
      console.log(`❌ Inline Citations Hooks: Error - ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Test lightweight inline citations service
   */
  static async testLightweightInlineCitations(): Promise<boolean> {
    console.log("\n🧪 Testing Lightweight Inline Citations Service...");
    
    try {
      const { LightweightInlineCitations } = await import("../lib/services/lightweight-inline-citations");
      const { InlineCitationsHooks } = await import("../lib/services/hooks/inline-citations-hooks");
      
      // Pre-populate cache with test images
      const { ServiceHookRegistry } = await import("../lib/services/hooks/service-hook-registry");
      await ServiceHookRegistry.executeHooks({
        serviceId: 'enhanced-doc-reader',
        operation: 'images-extracted',
        data: {
          documentId: 'doc-123',
          pages: mockExtractedImages
        }
      });

      // Test citation generation (this will use mocked methods)
      const result = await LightweightInlineCitations.generateInlineCitations(
        'test-message-id',
        'test-chat-id',
        'This is test content for citation generation.',
        {
          maxCitations: 5,
          minConfidence: 0.7,
          useCache: true,
          fallbackToExtraction: false
        }
      );

      if (result.success) {
        console.log("✅ Lightweight Inline Citations: Service working");
        console.log(`   Citations generated: ${result.citationCount}`);
        console.log(`   Image sources: ${result.imagesSources.length}`);
        return true;
      } else {
        console.log(`❌ Lightweight Inline Citations: Service failed - ${result.error}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Lightweight Inline Citations: Error - ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Test integration initialization
   */
  static async testIntegrationInitialization(): Promise<boolean> {
    console.log("\n🧪 Testing Integration Initialization...");
    
    try {
      const { LightweightCitationsIntegration } = await import("../lib/services/lightweight-citations-integration");
      
      // Test initialization
      await LightweightCitationsIntegration.initialize();
      
      if (LightweightCitationsIntegration.isInitialized()) {
        console.log("✅ Integration Initialization: Successfully initialized");
        
        // Test status
        const status = LightweightCitationsIntegration.getStatus();
        console.log(`   Hooks registered: ${status.hookStats.totalHooks}`);
        console.log(`   Services: ${status.hookStats.serviceCount}`);
        
        return true;
      } else {
        console.log("❌ Integration Initialization: Failed to initialize");
        return false;
      }
    } catch (error) {
      console.log(`❌ Integration Initialization: Error - ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Test service extensions (mock test since we can't load actual services)
   */
  static async testServiceExtensions(): Promise<boolean> {
    console.log("\n🧪 Testing Service Extensions...");
    
    try {
      // Test that extension modules can be imported
      await import("../lib/services/extensions/enhanced-doc-reader-extension");
      await import("../lib/services/extensions/rag-extension");
      
      console.log("✅ Service Extensions: Extension modules loaded successfully");
      return true;
    } catch (error) {
      console.log(`❌ Service Extensions: Error - ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Test end-to-end integration flow
   */
  static async testEndToEndFlow(): Promise<boolean> {
    console.log("\n🧪 Testing End-to-End Integration Flow...");
    
    try {
      const { LightweightCitationsIntegration, LightweightInlineCitations } = await import("../lib/services/lightweight-citations-integration");
      const { ServiceHookRegistry } = await import("../lib/services/hooks/service-hook-registry");
      
      // Step 1: Initialize integration
      await LightweightCitationsIntegration.initialize();
      
      // Step 2: Simulate Enhanced Doc Reader processing
      await ServiceHookRegistry.executeHooks({
        serviceId: 'enhanced-doc-reader',
        operation: 'images-extracted',
        data: {
          documentId: 'doc-456',
          pages: mockExtractedImages
        }
      });
      
      // Step 3: Generate citations using cached images
      const result = await LightweightInlineCitations.generateInlineCitations(
        'test-message-456',
        'test-chat-456',
        'This is a comprehensive test of the end-to-end flow.',
        { useCache: true, fallbackToExtraction: false }
      );
      
      // Step 4: Verify results
      if (result.success && result.imagesSources.length > 0) {
        console.log("✅ End-to-End Flow: Complete integration working");
        console.log(`   Flow: Enhanced Doc Reader → Hooks → Cache → Citations`);
        console.log(`   Images cached: ${result.imagesSources[0].imageCount}`);
        console.log(`   Citations: ${result.citationCount}`);
        return true;
      } else {
        console.log("❌ End-to-End Flow: Integration flow failed");
        return false;
      }
    } catch (error) {
      console.log(`❌ End-to-End Flow: Error - ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<boolean> {
    console.log("🔧 Testing Lightweight Citations Integration");
    console.log("============================================");

    const tests = [
      this.testServiceHookRegistry,
      this.testInlineCitationsHooks,
      this.testLightweightInlineCitations,
      this.testIntegrationInitialization,
      this.testServiceExtensions,
      this.testEndToEndFlow
    ];

    const results = [];
    for (const test of tests) {
      try {
        const result = await test.call(this);
        results.push(result);
      } catch (error) {
        console.log(`❌ Test failed with error: ${error instanceof Error ? error.message : String(error)}`);
        results.push(false);
      }
    }

    const allPassed = results.every(result => result);
    
    console.log("\n🎯 Overall Result:");
    console.log("==================");
    
    if (allPassed) {
      console.log("✅ ALL TESTS PASSED");
      console.log("✨ The lightweight citations integration is working correctly:");
      console.log("   • Service hook registry functioning");
      console.log("   • Image caching from Enhanced Doc Reader working");
      console.log("   • Lightweight citations service operational");
      console.log("   • Integration initialization successful");
      console.log("   • Service extensions loaded");
      console.log("   • End-to-end flow complete");
      console.log("");
      console.log("🚀 Ready for production use!");
    } else {
      console.log("❌ SOME TESTS FAILED");
      console.log("⚠️  Please review the failed tests above and address any issues.");
    }

    return allPassed;
  }
}

async function main() {
  const success = await LightweightCitationsIntegrationTest.runAllTests();
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as testLightweightCitationsIntegration };
