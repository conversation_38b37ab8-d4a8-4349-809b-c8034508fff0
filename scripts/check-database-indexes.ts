#!/usr/bin/env tsx

import { db } from "../lib/db";
import { sql } from "drizzle-orm";
import { Logger } from "../lib/utils/Logger";

/**
 * Check current database indexes and optimization status
 */
async function checkDatabaseIndexes() {
  try {
    Logger.info("🔍 Checking database indexes and optimization status...");
    
    // Check if pgvector extension is enabled
    const extensionCheck = await db.execute(sql`
      SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'vector'
      ) as vector_enabled;
    `);
    
    Logger.info("📦 Extension status:", {
      pgvector: extensionCheck[0]?.vector_enabled ? "✅ Enabled" : "❌ Not enabled"
    });
    
    // Check existing indexes on embeddings table
    const embeddingIndexes = await db.execute(sql`
      SELECT 
        indexname,
        indexdef,
        schemaname,
        tablename
      FROM pg_indexes 
      WHERE tablename = 'embeddings'
      ORDER BY indexname;
    `);
    
    Logger.info("📊 Current indexes on embeddings table:");
    embeddingIndexes.forEach((index: any) => {
      Logger.info(`  - ${index.indexname}: ${index.indexdef}`);
    });
    
    // Check for vector-specific indexes
    const vectorIndexes = await db.execute(sql`
      SELECT 
        i.relname as index_name,
        am.amname as index_method,
        pg_get_indexdef(i.oid) as index_definition
      FROM pg_class i
      JOIN pg_am am ON i.relam = am.oid
      JOIN pg_index ix ON i.oid = ix.indexrelid
      JOIN pg_class t ON ix.indrelid = t.oid
      WHERE t.relname = 'embeddings'
        AND am.amname IN ('hnsw', 'ivfflat')
      ORDER BY i.relname;
    `);
    
    Logger.info("🎯 Vector-specific indexes:");
    if (vectorIndexes.length === 0) {
      Logger.warn("  ❌ No vector indexes found (HNSW or IVFFlat)");
    } else {
      vectorIndexes.forEach((index: any) => {
        Logger.info(`  ✅ ${index.index_name} (${index.index_method}): ${index.index_definition}`);
      });
    }
    
    // Check table statistics
    const tableStats = await db.execute(sql`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_rows,
        n_dead_tup as dead_rows,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables 
      WHERE tablename IN ('embeddings', 'resources', 'source_documents')
      ORDER BY tablename;
    `);
    
    Logger.info("📈 Table statistics:");
    tableStats.forEach((stat: any) => {
      Logger.info(`  ${stat.tablename}:`, {
        liveRows: stat.live_rows,
        deadRows: stat.dead_rows,
        lastAnalyze: stat.last_analyze || stat.last_autoanalyze || "Never",
      });
    });
    
    // Check if optimization indexes exist
    const optimizationIndexes = [
      'embeddings_resource_embedding_idx',
      'source_documents_chat_id_idx', 
      'resources_source_document_idx',
      'embeddings_embedding_hnsw_idx',
      'message_documents_message_source_idx',
      'embeddings_recent_idx'
    ];
    
    Logger.info("🔧 Optimization index status:");
    for (const indexName of optimizationIndexes) {
      const exists = await db.execute(sql`
        SELECT EXISTS(
          SELECT 1 FROM pg_indexes WHERE indexname = ${indexName}
        ) as exists;
      `);
      
      const status = exists[0]?.exists ? "✅ Present" : "❌ Missing";
      Logger.info(`  ${indexName}: ${status}`);
    }
    
    return {
      vectorEnabled: extensionCheck[0]?.vector_enabled,
      totalIndexes: embeddingIndexes.length,
      vectorIndexes: vectorIndexes.length,
      tableStats,
      optimizationStatus: {
        applied: optimizationIndexes.length,
        // We'll check this in the actual implementation
      }
    };
    
  } catch (error) {
    Logger.error("❌ Error checking database indexes:", error);
    throw error;
  }
}

// Run the check if this script is executed directly
if (require.main === module) {
  checkDatabaseIndexes()
    .then((result) => {
      Logger.info("✅ Database index check completed", result);
      process.exit(0);
    })
    .catch((error) => {
      Logger.error("💥 Database index check failed:", error);
      process.exit(1);
    });
}

export { checkDatabaseIndexes };
