#!/usr/bin/env tsx

import { InlineCitationsChunker } from "../lib/services/inline-citations-chunker";
import { InlineCitationsPerformance, OPERATION_TYPES } from "../lib/services/inline-citations-performance";
import { countTokens } from "../lib/services/llmService";
import { Logger } from "../lib/utils/Logger";

/**
 * Simple performance benchmark for inline citations optimizations
 * Tests components that don't require database connections
 */

// Test configuration
const TEST_CONFIG = {
  ITERATIONS: 50,
  LARGE_ITERATIONS: 10,
  CHUNK_SIZES: [500, 1000, 2000, 5000],
} as const;

interface TestResult {
  component: string;
  testName: string;
  duration: number;
  iterations: number;
  avgPerIteration: number;
  status: "✅ PASS" | "⚠️ SLOW" | "❌ FAIL";
}

/**
 * Main benchmark function
 */
async function runSimpleBenchmarks(): Promise<void> {
  console.log("🚀 Starting Simple Inline Citations Performance Benchmarks");
  console.log("=" .repeat(60));
  
  const results: TestResult[] = [];
  
  try {
    // Reset performance metrics
    InlineCitationsPerformance.resetMetrics();
    
    // Test 1: Token counting performance
    console.log("📊 Testing token counting performance...");
    const tokenResults = await testTokenCountingPerformance();
    results.push(...tokenResults);
    
    // Test 2: Chunking algorithm performance
    console.log("📄 Testing chunking algorithm performance...");
    const chunkingResults = await testChunkingPerformance();
    results.push(...chunkingResults);
    
    // Test 3: Performance monitoring overhead
    console.log("⏱️ Testing performance monitoring overhead...");
    const monitoringResults = await testPerformanceMonitoringOverhead();
    results.push(...monitoringResults);
    
    // Display results
    displayResults(results);
    
    // Generate summary
    generateSummary(results);
    
  } catch (error) {
    console.error("❌ Benchmark failed:", error);
    process.exit(1);
  }
}

/**
 * Test token counting performance with different text sizes
 */
async function testTokenCountingPerformance(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  const testTexts = generateTestTexts();
  
  for (const [index, text] of testTexts.entries()) {
    const testName = `Token Counting (${text.length} chars)`;
    
    const startTime = performance.now();
    for (let i = 0; i < TEST_CONFIG.ITERATIONS; i++) {
      countTokens(text);
    }
    const duration = performance.now() - startTime;
    
    const avgPerIteration = duration / TEST_CONFIG.ITERATIONS;
    const status = avgPerIteration < 1 ? "✅ PASS" : avgPerIteration < 5 ? "⚠️ SLOW" : "❌ FAIL";
    
    results.push({
      component: "Token Counting",
      testName,
      duration,
      iterations: TEST_CONFIG.ITERATIONS,
      avgPerIteration,
      status,
    });
  }
  
  return results;
}

/**
 * Test chunking algorithm performance with different document sizes
 */
async function testChunkingPerformance(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  
  for (const size of TEST_CONFIG.CHUNK_SIZES) {
    const testDocument = generateTestDocument(size);
    const testName = `Chunking (${size} chars)`;
    
    const startTime = performance.now();
    for (let i = 0; i < TEST_CONFIG.LARGE_ITERATIONS; i++) {
      await InlineCitationsChunker.chunkMarkdownForInlineCitations(
        testDocument,
        `test_doc_${size}_${i}`
      );
    }
    const duration = performance.now() - startTime;
    
    const avgPerIteration = duration / TEST_CONFIG.LARGE_ITERATIONS;
    const status = avgPerIteration < 100 ? "✅ PASS" : avgPerIteration < 500 ? "⚠️ SLOW" : "❌ FAIL";
    
    results.push({
      component: "Chunking",
      testName,
      duration,
      iterations: TEST_CONFIG.LARGE_ITERATIONS,
      avgPerIteration,
      status,
    });
  }
  
  return results;
}

/**
 * Test performance monitoring overhead
 */
async function testPerformanceMonitoringOverhead(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  
  // Test without monitoring
  const startTimeWithout = performance.now();
  for (let i = 0; i < TEST_CONFIG.ITERATIONS; i++) {
    await simulateWork(10);
  }
  const durationWithout = performance.now() - startTimeWithout;
  
  // Test with monitoring
  const startTimeWith = performance.now();
  for (let i = 0; i < TEST_CONFIG.ITERATIONS; i++) {
    const timer = InlineCitationsPerformance.startTimer(
      `test_${i}`,
      OPERATION_TYPES.CHUNKING,
      { test: true }
    );
    await simulateWork(10);
    timer.end();
  }
  const durationWith = performance.now() - startTimeWith;
  
  const overhead = durationWith - durationWithout;
  const overheadPercent = (overhead / durationWithout) * 100;
  const status = overheadPercent < 10 ? "✅ PASS" : overheadPercent < 25 ? "⚠️ SLOW" : "❌ FAIL";
  
  results.push({
    component: "Performance Monitoring",
    testName: `Monitoring Overhead (${overheadPercent.toFixed(1)}%)`,
    duration: overhead,
    iterations: TEST_CONFIG.ITERATIONS,
    avgPerIteration: overhead / TEST_CONFIG.ITERATIONS,
    status,
  });
  
  return results;
}

/**
 * Generate test texts of various sizes
 */
function generateTestTexts(): string[] {
  return [
    "Short text for testing.",
    "This is a medium-length text that contains multiple words and should be used for testing token counting performance.",
    "This is a much longer text that contains multiple sentences and paragraphs. It should demonstrate the token counting algorithm performance with larger inputs. This text includes various types of content to test different scenarios.",
    Array(50).fill("This sentence will be repeated to create a very long text for performance testing purposes.").join(" "),
  ];
}

/**
 * Generate test document of specific size
 */
function generateTestDocument(targetSize: number): string {
  const baseContent = `
# Test Document

## Introduction
This is a test document for performance benchmarking.

## Section 1
Content for section 1 with multiple paragraphs.

### Subsection 1.1
More detailed content here.

## Section 2
\`\`\`javascript
function example() {
  return "test";
}
\`\`\`

## Conclusion
Final section of the document.
  `.trim();
  
  let document = baseContent;
  while (document.length < targetSize) {
    document += "\n\nAdditional content to reach target size. This includes various formatting and content types.";
  }
  
  return document.substring(0, targetSize);
}

/**
 * Simulate work for overhead testing
 */
async function simulateWork(ms: number): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Display benchmark results in a formatted table
 */
function displayResults(results: TestResult[]): void {
  console.log("\n📊 BENCHMARK RESULTS");
  console.log("=" .repeat(80));
  
  // Group by component
  const grouped = results.reduce((acc, result) => {
    if (!acc[result.component]) acc[result.component] = [];
    acc[result.component].push(result);
    return acc;
  }, {} as Record<string, TestResult[]>);
  
  Object.entries(grouped).forEach(([component, componentResults]) => {
    console.log(`\n🔍 ${component.toUpperCase()}`);
    console.log("-".repeat(50));
    
    componentResults.forEach(result => {
      console.log(`${result.status} ${result.testName}`);
      console.log(`   Duration: ${result.duration.toFixed(2)}ms`);
      console.log(`   Avg/iteration: ${result.avgPerIteration.toFixed(2)}ms`);
      console.log(`   Iterations: ${result.iterations}`);
    });
  });
}

/**
 * Generate performance summary
 */
function generateSummary(results: TestResult[]): void {
  const passed = results.filter(r => r.status === "✅ PASS").length;
  const slow = results.filter(r => r.status === "⚠️ SLOW").length;
  const failed = results.filter(r => r.status === "❌ FAIL").length;
  
  console.log("\n🎯 PERFORMANCE SUMMARY");
  console.log("=" .repeat(40));
  console.log(`Total Tests: ${results.length}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`⚠️ Slow: ${slow}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
  
  // Performance metrics summary
  InlineCitationsPerformance.logPerformanceSummary();
  
  if (failed > 0) {
    console.log("\n❌ PERFORMANCE ISSUES DETECTED");
    console.log("Some components are performing below expected thresholds.");
    console.log("Consider reviewing the optimization implementations.");
  } else if (slow > 0) {
    console.log("\n⚠️ PERFORMANCE WARNINGS");
    console.log("Some components are slower than optimal but within acceptable limits.");
  } else {
    console.log("\n🎉 ALL PERFORMANCE TESTS PASSED!");
    console.log("Inline citations optimizations are performing well.");
  }
}

// Run benchmarks if this file is executed directly
if (require.main === module) {
  runSimpleBenchmarks()
    .then(() => {
      console.log("\n✅ Benchmark completed successfully!");
    })
    .catch((error) => {
      console.error("❌ Benchmark failed:", error);
      process.exit(1);
    });
}

export { runSimpleBenchmarks };
