#!/usr/bin/env tsx
/**
 * Test Script: RAG Syntax Verification
 * 
 * This script tests that the RAG file has correct syntax and no duplicate exports
 * without requiring database access.
 * 
 * Usage: npx tsx scripts/test-rag-syntax.ts
 */

import * as fs from 'fs';
import * as path from 'path';

function testRAGSyntax() {
  console.log("🧪 Testing RAG file syntax...");
  
  try {
    const ragFilePath = path.join(process.cwd(), 'lib/ai/rag.ts');
    const ragContent = fs.readFileSync(ragFilePath, 'utf8');
    
    console.log("✅ RAG file can be read");
    
    // Check for duplicate exports
    const exportMatches = ragContent.match(/^export\s+(interface|class|function|async\s+function)\s+(\w+)/gm);
    
    if (exportMatches) {
      console.log(`📦 Found ${exportMatches.length} export statements`);
      
      const exportNames: string[] = [];
      const duplicates: string[] = [];
      
      for (const match of exportMatches) {
        const nameMatch = match.match(/^export\s+(?:interface|class|function|async\s+function)\s+(\w+)/);
        if (nameMatch) {
          const name = nameMatch[1];
          if (exportNames.includes(name)) {
            duplicates.push(name);
          } else {
            exportNames.push(name);
          }
        }
      }
      
      console.log(`📋 Export names: ${exportNames.join(', ')}`);
      
      if (duplicates.length > 0) {
        console.log(`❌ Duplicate exports found: ${duplicates.join(', ')}`);
        return false;
      } else {
        console.log("✅ No duplicate exports found");
      }
    } else {
      console.log("⚠️  No export statements found");
    }
    
    // Check for expected exports
    const expectedExports = ['Document', 'DocumentChunk', 'RAGPipeline', 'refineUserPromptForRAG'];
    let allExportsFound = true;
    
    for (const expectedExport of expectedExports) {
      const exportRegex = new RegExp(`export\\s+(?:interface|class|function|async\\s+function)\\s+${expectedExport}`, 'm');
      if (exportRegex.test(ragContent)) {
        console.log(`✅ ${expectedExport} - Found`);
      } else {
        console.log(`❌ ${expectedExport} - Missing`);
        allExportsFound = false;
      }
    }
    
    // Check file structure
    const lines = ragContent.split('\n');
    console.log(`📏 File has ${lines.length} lines`);
    
    // Check for orphaned code (code outside of classes/functions/interfaces)
    const lastExportLine = ragContent.lastIndexOf('export');
    const lastClosingBrace = ragContent.lastIndexOf('}');
    
    if (lastClosingBrace > lastExportLine) {
      console.log("✅ File structure appears correct");
    } else {
      console.log("⚠️  Potential file structure issue detected");
    }
    
    return allExportsFound;
    
  } catch (error) {
    console.log(`❌ Failed to read RAG file: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

async function main() {
  console.log("🔧 RAG Syntax Test");
  console.log("==================");
  console.log("");

  const testsPassed = testRAGSyntax();

  console.log("\n🎯 Overall Result:");
  console.log("==================");
  
  if (testsPassed) {
    console.log("✅ ALL TESTS PASSED");
    console.log("✨ The RAG file syntax is correct:");
    console.log("   • No duplicate export errors");
    console.log("   • All expected exports are present");
    console.log("   • File structure is valid");
    console.log("   • No orphaned code detected");
  } else {
    console.log("❌ SOME TESTS FAILED");
    console.log("⚠️  Please review the failed tests above and address any issues.");
  }

  process.exit(testsPassed ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as testRAGSyntax };
