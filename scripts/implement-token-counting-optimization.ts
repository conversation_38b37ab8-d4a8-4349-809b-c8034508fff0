#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to implement the missing token counting optimization
 * This will replace the current simple implementation with the optimized version
 * described in the inline citations optimization document
 */

import * as fs from 'fs';
import * as path from 'path';

const LLM_SERVICE_PATH = path.join(__dirname, '../lib/services/llmService.ts');

/**
 * Implements the optimized token counting function with LRU cache
 */
const OPTIMIZED_TOKEN_COUNTING_CODE = `
// LRU Cache for token counting optimization
class TokenCountCache {
  private cache = new Map<string, number>();
  private maxSize = 1000; // Cache up to 1000 entries

  get(text: string): number | undefined {
    const cached = this.cache.get(text);
    if (cached !== undefined) {
      // Move to end (most recently used)
      this.cache.delete(text);
      this.cache.set(text, cached);
      return cached;
    }
    return undefined;
  }

  set(text: string, count: number): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(text, count);
  }

  clear(): void {
    this.cache.clear();
  }
}

// Global cache instance
const tokenCountCache = new TokenCountCache();

/**
 * Optimized token counting with improved heuristics and caching
 * Combines character-based and word-based estimation for better accuracy
 * Includes LRU cache for frequently processed text
 *
 * @param text - The input string
 * @returns Estimated token count
 */
export function countTokens(text: string): number {
  // Short text optimization - bypass cache for very short text
  if (text.length < 10) {
    return Math.ceil(text.length / 4);
  }

  // Check cache first
  const cached = tokenCountCache.get(text);
  if (cached !== undefined) {
    return cached;
  }

  // Improved heuristic combining multiple approaches
  const characters = text.length;
  const words = text.split(/\\s+/).filter(word => word.length > 0).length;

  const charBasedTokens = Math.ceil(characters / 4);
  const wordBasedTokens = Math.ceil(words * 1.3);
  const tokenCount = Math.round((charBasedTokens + wordBasedTokens) / 2);

  // Cache the result
  tokenCountCache.set(text, tokenCount);

  return tokenCount;
}

/**
 * Clears the token counting cache (useful for testing)
 */
export function clearTokenCountCache(): void {
  tokenCountCache.clear();
}
`;

/**
 * Backs up the original file
 */
function backupOriginalFile(): void {
  const backupPath = LLM_SERVICE_PATH + '.backup';
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(LLM_SERVICE_PATH, backupPath);
    console.log('✅ Original file backed up to:', backupPath);
  } else {
    console.log('ℹ️ Backup already exists:', backupPath);
  }
}

/**
 * Implements the token counting optimization
 */
function implementOptimization(): void {
  console.log('🔧 Implementing token counting optimization...');

  // Read the current file
  const content = fs.readFileSync(LLM_SERVICE_PATH, 'utf8');

  // Find the current countTokens function
  const currentFunctionRegex = /\/\*\*[\s\S]*?\*\/\s*export function countTokens\(text: string\): number \{[\s\S]*?\n\}/;

  if (!currentFunctionRegex.test(content)) {
    console.error('❌ Could not find the current countTokens function');
    console.log('Looking for pattern: export function countTokens(text: string): number');
    return;
  }

  // Replace the function with the optimized version
  const updatedContent = content.replace(currentFunctionRegex, OPTIMIZED_TOKEN_COUNTING_CODE.trim());

  // Write the updated content
  fs.writeFileSync(LLM_SERVICE_PATH, updatedContent);

  console.log('✅ Token counting optimization implemented successfully!');
  console.log('📈 Expected improvements:');
  console.log('  • 15-25% faster token counting');
  console.log('  • LRU cache for repeated text');
  console.log('  • Improved accuracy with combined heuristics');
}

/**
 * Validates the implementation
 */
function validateImplementation(): void {
  console.log('🔍 Validating implementation...');

  const content = fs.readFileSync(LLM_SERVICE_PATH, 'utf8');

  const checks = [
    { name: 'TokenCountCache class', pattern: /class TokenCountCache/ },
    { name: 'LRU cache logic', pattern: /cache\.delete.*cache\.set/ },
    { name: 'Optimized countTokens function', pattern: /charBasedTokens.*wordBasedTokens/ },
    { name: 'Cache integration', pattern: /tokenCountCache\.get.*tokenCountCache\.set/ },
    { name: 'clearTokenCountCache function', pattern: /export function clearTokenCountCache/ },
  ];

  let allPassed = true;

  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name}`);
    } else {
      console.log(`❌ ${check.name}`);
      allPassed = false;
    }
  });

  if (allPassed) {
    console.log('🎉 All validation checks passed!');
  } else {
    console.log('⚠️ Some validation checks failed. Please review the implementation.');
  }
}

/**
 * Restores the original file from backup
 */
function restoreOriginal(): void {
  const backupPath = LLM_SERVICE_PATH + '.backup';
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, LLM_SERVICE_PATH);
    console.log('✅ Original file restored from backup');
  } else {
    console.log('❌ No backup file found');
  }
}

/**
 * Main function
 */
function main(): void {
  const args = process.argv.slice(2);

  if (args.includes('--restore')) {
    restoreOriginal();
    return;
  }

  if (args.includes('--validate-only')) {
    validateImplementation();
    return;
  }

  console.log('🚀 Token Counting Optimization Implementation');
  console.log('=' .repeat(50));

  try {
    // Check if file exists
    if (!fs.existsSync(LLM_SERVICE_PATH)) {
      console.error('❌ llmService.ts not found at:', LLM_SERVICE_PATH);
      process.exit(1);
    }

    // Backup original file
    backupOriginalFile();

    // Implement optimization
    implementOptimization();

    // Validate implementation
    validateImplementation();

    console.log('\n🎯 IMPLEMENTATION COMPLETE');
    console.log('Next steps:');
    console.log('1. Run performance benchmarks to validate improvements');
    console.log('2. Test the application to ensure no regressions');
    console.log('3. Deploy to production after validation');
    console.log('\nTo restore original: pnpm tsx scripts/implement-token-counting-optimization.ts --restore');

  } catch (error) {
    console.error('❌ Implementation failed:', error);
    console.log('Attempting to restore original file...');
    restoreOriginal();
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { main as implementTokenCountingOptimization };
