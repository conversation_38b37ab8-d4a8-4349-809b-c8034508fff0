import { db } from "../lib/db";
import { sql } from "drizzle-orm";

/**
 * Simple script to apply inline citations database indexes
 */
async function applyInlineCitationsIndexes() {
  try {
    console.log("🚀 Applying inline citations database indexes...\n");

    const indexes = [
      {
        name: "embeddings_resource_embedding_idx",
        sql: `CREATE INDEX IF NOT EXISTS embeddings_resource_embedding_idx
              ON embeddings (resource_id)
              INCLUDE (embedding, content, created_at)`,
        description: "Composite index for embeddings table"
      },
      {
        name: "source_documents_chat_id_idx",
        sql: `CREATE INDEX IF NOT EXISTS source_documents_chat_id_idx
              ON source_documents (chat_id)
              INCLUDE (id, filename, created_at)`,
        description: "Index for chat-based filtering"
      },
      {
        name: "resources_source_document_idx",
        sql: `CREATE INDEX IF NOT EXISTS resources_source_document_idx
              ON resources (source_document_id)
              INCLUDE (id, content, created_at)`,
        description: "Composite index for resources table"
      },
      {
        name: "message_documents_message_source_idx",
        sql: `CREATE INDEX IF NOT EXISTS message_documents_message_source_idx
              ON message_documents (message_id, source_document_id)`,
        description: "Index for message-document associations"
      }
    ];

    // Apply basic indexes first
    for (const index of indexes) {
      try {
        console.log(`📝 Creating ${index.name}...`);
        await db.execute(sql.raw(index.sql));
        console.log(`  ✅ ${index.description}`);
      } catch (error) {
        console.log(`  ❌ Failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Try to create vector index (might fail if extension not available)
    console.log("\n🧮 Attempting vector index creation...");
    try {
      // Drop old index first
      await db.execute(sql`DROP INDEX IF EXISTS embeddings_embedding_idx`);
      console.log("  ✅ Dropped old vector index");

      // Create optimized HNSW vector index
      await db.execute(sql.raw(`
        CREATE INDEX IF NOT EXISTS embeddings_embedding_hnsw_idx
        ON embeddings
        USING hnsw (embedding vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
      `));
      console.log("  ✅ Created HNSW vector index");
    } catch (error) {
      console.log(`  ⚠️  Vector index failed: ${error instanceof Error ? error.message : String(error)}`);
      console.log("  💡 This is normal if vector extension is not installed");
    }

    // Update table statistics
    console.log("\n📊 Updating table statistics...");
    try {
      await db.execute(sql`ANALYZE embeddings`);
      await db.execute(sql`ANALYZE resources`);
      await db.execute(sql`ANALYZE source_documents`);
      await db.execute(sql`ANALYZE message_documents`);
      console.log("  ✅ Statistics updated");
    } catch (error) {
      console.log(`  ⚠️  Statistics update failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    console.log("\n🎉 Index application completed!");
    console.log("💡 Run the check script to verify: pnpm tsx scripts/check-inline-citations-db.ts");

  } catch (error) {
    console.error("❌ Index application failed:", error);
    process.exit(1);
  }
}

// Run the application
if (require.main === module) {
  applyInlineCitationsIndexes()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

export { applyInlineCitationsIndexes };
