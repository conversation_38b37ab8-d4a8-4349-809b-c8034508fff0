#!/usr/bin/env tsx

/**
 * Performance comparison script for sequential vs parallel PDF processing
 * Demonstrates the expected performance improvements with parallel processing
 */

interface ProcessingScenario {
  name: string;
  pages: number;
  pageProcessingTimeMs: number;
  description: string;
}

interface ProcessingResult {
  scenario: string;
  sequentialTimeMs: number;
  parallelTimeMs: number;
  improvementPercent: number;
  improvementRatio: string;
}

// Configuration from the parallel processing implementation
const PARALLEL_CONFIG = {
  MAX_CONCURRENT_PAGES: 5,
  BATCH_SIZE: 10,
  RETRY_ATTEMPTS: 2,
  RETRY_DELAY_MS: 1000,
} as const;

/**
 * Calculate sequential processing time
 */
function calculateSequentialTime(pages: number, pageTimeMs: number): number {
  return pages * pageTimeMs;
}

/**
 * Calculate parallel processing time with batching and concurrency
 */
function calculateParallelTime(pages: number, pageTimeMs: number): number {
  const batches = Math.ceil(pages / PARALLEL_CONFIG.BATCH_SIZE);
  let totalTime = 0;

  for (let batch = 0; batch < batches; batch++) {
    const batchStart = batch * PARALLEL_CONFIG.BATCH_SIZE + 1;
    const batchEnd = Math.min(batchStart + PARALLEL_CONFIG.BATCH_SIZE - 1, pages);
    const batchPages = batchEnd - batchStart + 1;
    
    // Time for this batch = ceil(pages_in_batch / max_concurrent) * page_time
    const batchTime = Math.ceil(batchPages / PARALLEL_CONFIG.MAX_CONCURRENT_PAGES) * pageTimeMs;
    totalTime += batchTime;
  }

  return totalTime;
}

/**
 * Run performance comparison analysis
 */
function runPerformanceComparison() {
  console.log("📊 PDF Processing Performance Comparison");
  console.log("=======================================");
  console.log("Sequential vs Parallel Processing Analysis\n");

  // Define test scenarios
  const scenarios: ProcessingScenario[] = [
    {
      name: "Small Document",
      pages: 5,
      pageProcessingTimeMs: 2000,
      description: "Typical small PDF (5 pages, 2s per page)"
    },
    {
      name: "Medium Document",
      pages: 15,
      pageProcessingTimeMs: 2000,
      description: "Medium-sized PDF (15 pages, 2s per page)"
    },
    {
      name: "Large Document",
      pages: 30,
      pageProcessingTimeMs: 2000,
      description: "Large PDF (30 pages, 2s per page)"
    },
    {
      name: "Very Large Document",
      pages: 50,
      pageProcessingTimeMs: 2000,
      description: "Very large PDF (50 pages, 2s per page)"
    },
    {
      name: "Complex Pages",
      pages: 20,
      pageProcessingTimeMs: 3000,
      description: "Complex pages (20 pages, 3s per page)"
    },
    {
      name: "Fast Processing",
      pages: 25,
      pageProcessingTimeMs: 1000,
      description: "Fast processing (25 pages, 1s per page)"
    }
  ];

  const results: ProcessingResult[] = [];

  // Calculate performance for each scenario
  for (const scenario of scenarios) {
    const sequentialTime = calculateSequentialTime(scenario.pages, scenario.pageProcessingTimeMs);
    const parallelTime = calculateParallelTime(scenario.pages, scenario.pageProcessingTimeMs);
    const improvement = ((sequentialTime - parallelTime) / sequentialTime) * 100;
    const ratio = (sequentialTime / parallelTime).toFixed(1);

    results.push({
      scenario: scenario.name,
      sequentialTimeMs: sequentialTime,
      parallelTimeMs: parallelTime,
      improvementPercent: improvement,
      improvementRatio: ratio,
    });

    console.log(`🔍 ${scenario.name}`);
    console.log(`   ${scenario.description}`);
    console.log(`   Sequential: ${(sequentialTime / 1000).toFixed(1)}s`);
    console.log(`   Parallel:   ${(parallelTime / 1000).toFixed(1)}s`);
    console.log(`   Improvement: ${improvement.toFixed(1)}% (${ratio}x faster)`);
    console.log("");
  }

  // Summary statistics
  console.log("📈 Performance Summary");
  console.log("=====================");
  
  const avgImprovement = results.reduce((sum, r) => sum + r.improvementPercent, 0) / results.length;
  const maxImprovement = Math.max(...results.map(r => r.improvementPercent));
  const minImprovement = Math.min(...results.map(r => r.improvementPercent));

  console.log(`Average improvement: ${avgImprovement.toFixed(1)}%`);
  console.log(`Maximum improvement: ${maxImprovement.toFixed(1)}%`);
  console.log(`Minimum improvement: ${minImprovement.toFixed(1)}%`);

  // Configuration analysis
  console.log("\n⚙️  Configuration Analysis");
  console.log("=========================");
  console.log(`Max concurrent pages: ${PARALLEL_CONFIG.MAX_CONCURRENT_PAGES}`);
  console.log(`Batch size: ${PARALLEL_CONFIG.BATCH_SIZE}`);
  console.log(`Retry attempts: ${PARALLEL_CONFIG.RETRY_ATTEMPTS}`);

  // Scaling analysis
  console.log("\n📊 Scaling Analysis");
  console.log("==================");
  
  const scalingTests = [1, 5, 10, 20, 50, 100];
  
  console.log("Pages | Sequential | Parallel | Improvement");
  console.log("------|------------|----------|------------");
  
  for (const pages of scalingTests) {
    const seq = calculateSequentialTime(pages, 2000);
    const par = calculateParallelTime(pages, 2000);
    const imp = ((seq - par) / seq * 100).toFixed(1);
    
    console.log(`${pages.toString().padStart(5)} | ${(seq/1000).toFixed(1).padStart(10)}s | ${(par/1000).toFixed(1).padStart(8)}s | ${imp.padStart(10)}%`);
  }

  // Resource utilization analysis
  console.log("\n💾 Resource Utilization");
  console.log("======================");
  
  const resourceTests = [
    { pages: 10, concurrent: 5, description: "Optimal utilization" },
    { pages: 3, concurrent: 3, description: "Under-utilized (fewer pages than max concurrent)" },
    { pages: 50, concurrent: 5, description: "Full utilization (many pages)" },
  ];

  for (const test of resourceTests) {
    const utilization = Math.min(test.pages, test.concurrent) / PARALLEL_CONFIG.MAX_CONCURRENT_PAGES * 100;
    console.log(`${test.pages} pages: ${utilization.toFixed(0)}% utilization - ${test.description}`);
  }

  // Memory management analysis
  console.log("\n🧠 Memory Management");
  console.log("===================");
  
  const memoryTests = [25, 50, 100];
  
  for (const pages of memoryTests) {
    const batches = Math.ceil(pages / PARALLEL_CONFIG.BATCH_SIZE);
    const maxPagesInMemory = Math.min(PARALLEL_CONFIG.BATCH_SIZE, pages);
    const maxConcurrentInMemory = Math.min(maxPagesInMemory, PARALLEL_CONFIG.MAX_CONCURRENT_PAGES);
    
    console.log(`${pages} pages:`);
    console.log(`  Batches: ${batches}`);
    console.log(`  Max pages in memory: ${maxPagesInMemory}`);
    console.log(`  Max concurrent in memory: ${maxConcurrentInMemory}`);
  }

  console.log("\n🎯 Key Benefits");
  console.log("==============");
  console.log("✅ Significantly reduced processing time for large documents");
  console.log("✅ Better resource utilization (CPU, memory, I/O)");
  console.log("✅ Controlled memory usage through batching");
  console.log("✅ Robust error handling with isolated failures");
  console.log("✅ Maintains page order and compatibility");
  console.log("✅ Configurable concurrency for different environments");
  console.log("✅ Detailed performance monitoring and statistics");

  console.log("\n🚀 Production Impact");
  console.log("===================");
  console.log("📈 User Experience: Faster document processing");
  console.log("💰 Cost Efficiency: Better resource utilization");
  console.log("🔧 Scalability: Handles larger documents efficiently");
  console.log("🛡️  Reliability: Isolated error handling");
  console.log("📊 Monitoring: Comprehensive performance metrics");
}

// Run the analysis
runPerformanceComparison();
