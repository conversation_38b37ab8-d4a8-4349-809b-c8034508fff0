import { db } from "../lib/db";
import { sql } from "drizzle-orm";
import { Logger } from "../lib/utils/Logger";
import fs from "fs";
import path from "path";

/**
 * <PERSON><PERSON>t to apply database optimizations for inline citations pipeline
 * This script applies indexes and optimizations to improve similarity search performance
 */
async function optimizeInlineCitationsDatabase() {
  try {
    Logger.info("Starting inline citations database optimization...");

    // Read the optimization SQL file
    const sqlFilePath = path.join(__dirname, "../lib/db/optimize-indexes.sql");
    const optimizationSQL = fs.readFileSync(sqlFilePath, "utf8");

    // Split SQL commands by semicolon and filter out empty commands
    const sqlCommands = optimizationSQL
      .split(";")
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith("--"));

    Logger.info(`Executing ${sqlCommands.length} optimization commands...`);

    // Execute each SQL command
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];
      
      try {
        Logger.debug(`Executing command ${i + 1}/${sqlCommands.length}`, {
          command: command.substring(0, 100) + (command.length > 100 ? "..." : ""),
        });

        await db.execute(sql.raw(command));
        
        Logger.debug(`Command ${i + 1} executed successfully`);
      } catch (error) {
        // Log error but continue with other commands
        Logger.warn(`Command ${i + 1} failed, continuing with others`, {
          command: command.substring(0, 100),
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Verify that key indexes exist
    await verifyIndexes();

    Logger.info("Database optimization completed successfully");

  } catch (error) {
    Logger.error("Database optimization failed", { error });
    throw error;
  }
}

/**
 * Verifies that critical indexes for inline citations exist
 */
async function verifyIndexes() {
  Logger.info("Verifying critical indexes...");

  const indexQueries = [
    {
      name: "embeddings_resource_embedding_idx",
      query: sql`
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'embeddings' 
        AND indexname = 'embeddings_resource_embedding_idx'
      `,
    },
    {
      name: "embeddings_embedding_hnsw_idx",
      query: sql`
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'embeddings' 
        AND indexname = 'embeddings_embedding_hnsw_idx'
      `,
    },
    {
      name: "source_documents_chat_id_idx",
      query: sql`
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'source_documents' 
        AND indexname = 'source_documents_chat_id_idx'
      `,
    },
  ];

  for (const indexCheck of indexQueries) {
    try {
      const result = await db.execute(indexCheck.query);
      
      if (result.length > 0) {
        Logger.info(`✓ Index ${indexCheck.name} exists`);
      } else {
        Logger.warn(`✗ Index ${indexCheck.name} not found`);
      }
    } catch (error) {
      Logger.warn(`Could not verify index ${indexCheck.name}`, { error });
    }
  }
}

/**
 * Analyzes current database performance for inline citations
 */
async function analyzePerformance() {
  Logger.info("Analyzing database performance...");

  try {
    // Check table sizes
    const tableSizes = await db.execute(sql`
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
      FROM pg_tables 
      WHERE tablename IN ('embeddings', 'resources', 'source_documents', 'message_documents')
      ORDER BY size_bytes DESC
    `);

    Logger.info("Table sizes", { tableSizes });

    // Check index usage
    const indexUsage = await db.execute(sql`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan as index_scans,
        idx_tup_read as tuples_read,
        idx_tup_fetch as tuples_fetched
      FROM pg_stat_user_indexes 
      WHERE tablename IN ('embeddings', 'resources', 'source_documents')
      ORDER BY idx_scan DESC
    `);

    Logger.info("Index usage statistics", { indexUsage });

  } catch (error) {
    Logger.warn("Could not analyze performance", { error });
  }
}

// Main execution
if (require.main === module) {
  optimizeInlineCitationsDatabase()
    .then(() => {
      Logger.info("Optimization script completed successfully");
      return analyzePerformance();
    })
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      Logger.error("Optimization script failed", { error });
      process.exit(1);
    });
}

export { optimizeInlineCitationsDatabase, verifyIndexes, analyzePerformance };
