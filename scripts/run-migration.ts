import { addAvatarUrlColumn } from "../lib/db/migrations/scripts/add-avatar-url";
import { addPendingDocumentAssociationsTable } from "../lib/db/migrations/scripts/add-pending-document-associations";
import { addExtractedTextColumn } from "../lib/db/migrations/scripts/add-extracted-text";
import { addChatOrganizationTables } from "../lib/db/migrations/scripts/add-chat-organization-tables";
import { addStatusColumn } from "../lib/db/migrations/scripts/add-status-column";
import { addMissingUserFields } from "../lib/db/migrations/scripts/add-missing-user-fields";
import { updateChatTagsWithColors } from "../lib/db/migrations/scripts/update-chat-tags-with-colors";
import { addReferralCodeColumn } from "../lib/db/migrations/scripts/add-referral-code";
import { addExtendedTrialColumn } from "../lib/db/migrations/scripts/add-extended-trial-column";
import { addHeardFromColumn } from "../lib/db/migrations/scripts/add-heard-from-column";
import { addMissingChatDocumentFields } from "../lib/db/migrations/scripts/add-missing-chat-document-fields";
import { makeDocumentFolderIdNullable } from "../lib/db/migrations/scripts/make-document-folder-id-nullable";

async function runMigrations() {
  try {
    console.log("Running migrations...");

    // Run migrations
    // await addPendingDocumentAssociationsTable();
    // await addAvatarUrlColumn();
    // await addExtractedTextColumn();
    // await addStatusColumn();
    // await addMissingUserFields();
    // await addChatOrganizationTables();
    // await updateChatTagsWithColors();
    // await addReferralCodeColumn();
    
    // Run the extended trial column migration
    // await addExtendedTrialColumn();
    // Run the referral code migration
    // await addReferralCodeColumn();
    
    // Run the heard_from column migration
    // await addHeardFromColumn();
    
    // Run the missing chat and document fields migration
    // await addMissingChatDocumentFields();
    // await addDocumentChangeLogTable();
    
    // Make document folder_id nullable
    await makeDocumentFolderIdNullable();

    console.log("Migrations completed successfully");
    process.exit(0);
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

runMigrations();
